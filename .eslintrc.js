module.exports = {
  root: true,
  env: {
    browser: true,
    node: true
  },
  extends: [
    'plugin:vue/essential',
    '@vue/standard'
  ],
  parserOptions: {
    parser: 'babel-eslint'
  },
  rules: {
    'no-console': 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    // allow async-await
    'generator-star-spacing': 'off',
    // allow debugger during development
    'no-tabs': 'off',
    'eol-last': 'off',
    'indent': 'off',
    'quotes': 'off',
    'no-trailing-spaces': 'off',
    'no-mixed-spaces-and-tabs': 'off',
    'space-before-function-paren': 'off',
    'space-before-blocks': 'off',
    'no-multiple-empty-lines': 'off',
    'spaced-comment': 'off',
    'promise/catch-or-return': 2,
    'semi': 'off',
    // 禁止出现无用的表达式
    'no-unused-expressions': 'off',
    'space-infix-ops': 'off',
    'no-unused-vars': [
      'warn',
      {
        vars: 'local',
        args: 'none',
        ignoreRestSiblings: false
      }
    ],
    "vue/no-unused-components": 1,
    "comma-dangle": "off",
  },
  settings: {
    'import/resolver': {
      alias: {
        map: [
          ["@", "./src"]
        ],
        extendions: ['.ts', '.js', '.jsx', '.json', '.vue']
      }
    }
  }
}
