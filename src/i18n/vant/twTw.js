export default {
  name: '<PERSON>',
  tel: '<PERSON><PERSON>',
  save: '<PERSON><PERSON>',
  confirm: 'Si pi',
  cancel: 'Twam',
  delete: 'P<PERSON><PERSON>',
  complete: 'Wie',
  loading: 'Wɔde nneɛma regu mu...',
  telEmpty: 'Yɛsrɛ sɛ monhyɛ tel',
  nameEmpty: 'Yɛsrɛ sɛ hyehyɛ edin no',
  nameInvalid: 'Edin a wɔanhyehyɛ no yiye',
  confirmDelete: 'So wugye di sɛ wopɛ sɛ wopopa?',
  telInvalid: 'Telefon nɔma a ɛnteɛ',
  vanCalendar: {
    end: 'Awieeɛ',
    start: 'Hyɛ aseɛ',
    title: 'Kalenda a wɔde yɛ adwuma',
    startEnd: '<PERSON><PERSON><PERSON>/Awiei',
    weekdays: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    monthTitle: (year, month) => `${year}/${month}`,
    rangePrompt: (maxRange) => `Mpaw nea ɛboro saa ${maxRange} nna`,
  },
  vanCascader: {
    select: '<PERSON> ho',
  },
  vanContactCard: {
    addText: 'Fa nkitahodi ho nsɛm ka ho',
  },
  vanContactList: {
    addText: 'Fa nkitahodi foforo ka ho',
  },
  vanPagination: {
    prev: 'Dada',
    next: 'Deɛ ɛdi hɔ',
  },
  vanPullRefresh: {
    pulling: 'Twe na woanya ahoɔden foforo...',
    loosing: 'Loose sɛ wobɛsan ayɛ foforo...',
  },
  vanSubmitBar: {
    label: 'Ne nyinaa:',
  },
  vanCoupon: {
    unlimited: 'Anohyeto biara nni ho',
    discount: (discount) => `${discount * 10}% adum`,
    condition: (condition) => `Anyɛ bie koraa ${condition}`,
  },
  vanCouponCell: {
    title: 'Coupon no',
    tips: 'Coupons biara nni hɔ',
    count: (count) => `Wowɔ ${count} coupon ahorow a wɔde ma`,
  },
  vanCouponList: {
    empty: 'Coupons biara nni hɔ',
    exchange: 'Sesa',
    close: 'To mu',
    enable: 'Wɔ hɔ',
    disabled: 'Ɛnyɛ nea wobetumi anya',
    placeholder: 'Coupon koodu no',
  },
  vanAddressEdit: {
    area: 'Beaeɛ',
    postal: 'Postal',
    areaEmpty: 'Yɛsrɛ sɛ paw beae bi a wobegye',
    addressEmpty: 'Address ntumi nyɛ hunu',
    postalEmpty: 'Postal code a ɛnteɛ',
    defaultAddress: 'Set sɛ address a wɔde ahyɛ mu',
    telPlaceholder: 'Fon',
    namePlaceholder: 'Din',
    areaPlaceholder: 'Beaeɛ',
  },
  vanAddressEditDetail: {
    label: 'Adrɛse',
    placeholder: 'Adrɛse',
  },
  vanAddressList: {
    add: 'Fa address foforo ka ho',
  },
};