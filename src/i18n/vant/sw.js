export default {
  name: '<PERSON><PERSON>',
  tel: '<PERSON><PERSON>',
  save: '<PERSON><PERSON><PERSON>',
  confirm: 'T<PERSON><PERSON><PERSON>',
  cancel: '<PERSON><PERSON><PERSON>',
  delete: '<PERSON><PERSON>',
  complete: '<PERSON><PERSON><PERSON><PERSON>',
  loading: 'Inapakia...',
  telEmpty: '<PERSON><PERSON><PERSON><PERSON> jaza simu',
  nameEmpty: '<PERSON><PERSON><PERSON><PERSON> jaza jina',
  nameInvalid: '<PERSON><PERSON> lili<PERSON>',
  confirmDelete: 'Je, una uhakika unataka kufuta?',
  telInvalid: '<PERSON>bari ya simu iliyoharibika',
  vanCalendar: {
    end: '<PERSON><PERSON>sh<PERSON>',
    start: '<PERSON><PERSON>',
    title: '<PERSON>len<PERSON>',
    startEnd: '<PERSON><PERSON>/Mwisho',
    weekdays: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    monthTitle: (year, month) => `${year}/${month}`,
    rangePrompt: (maxRange) => `Chagua si zaidi ya ${maxRange} siku`
  },
  vanCascader: {
    select: '<PERSON><PERSON>'
  },
  vanContactCard: {
    addText: '<PERSON><PERSON><PERSON> maelezo ya mawasiliano'
  },
  vanContactList: {
    addText: 'Ongeza anwani mpya'
  },
  vanPagination: {
    prev: 'Iliyotangulia',
    next: 'Inayofuata'
  },
  vanPullRefresh: {
    pulling: 'Vuta ili kuonyesha upya...',
    loosing: 'Huru ili kuonyesha upya...'
  },
  vanSubmitBar: {
    label: 'Jumla:'
  },
  vanCoupon: {
    unlimited: 'Bila kikomo',
    discount: (discount) => `${discount * 10}% imezimwa`,
    condition: (condition) => `Angalau ${condition}`
  },
  vanCouponCell: {
    title: 'Kuponi',
    tips: 'Hakuna kuponi',
    count: (count) => `Unayo ${count} kuponi`
  },
  vanCouponList: {
    empty: 'Hakuna kuponi',
    exchange: 'Kubadilishana',
    close: 'Funga',
    enable: 'Inapatikana',
    disabled: 'Haipatikani',
    placeholder: 'Msimbo wa kuponi'
  },
  vanAddressEdit: {
    area: 'Eneo',
    postal: 'Posta',
    areaEmpty: 'Tafadhali chagua eneo la kupokea',
    addressEmpty: 'Anwani haiwezi kuwa tupu',
    postalEmpty: 'Msimbo wa posta usio sahihi',
    defaultAddress: 'Weka kama anwani chaguo-msingi',
    telPlaceholder: 'Simu',
    namePlaceholder: 'Jina',
    areaPlaceholder: 'Eneo'
  },
  vanAddressEditDetail: {
    label: 'Anwani',
    placeholder: 'Anwani'
  },
  vanAddressList: {
    add: 'Ongeza anwani mpya'
  }
};