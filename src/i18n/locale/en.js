module.exports = {
  home: {
    bannerText: 'Build an efficient team starting from {appName}',
    functionMenu: 'Function Menu',
    attendance: 'Attendance',
    myProfile: 'My Profile',
    todoItems: 'To-do items',
    supplement: 'Supplement personal information',
    supplementModify: 'There is a problem with your employment information, please check',
    deadline: 'Deadline',
    clockIn: 'Clock in',
    clockOut: 'Clock out',
    toComplete: 'To complete',
    rejectedSystem: 'You are currently not eligible for employment',
    logout: 'Logout',
    return: 'Return'
  },
  logout: {
    confirmText: 'Confirm logout?',
    confirm: 'Confirm',
    cancel: 'Cancel',
  },
  attendance: {
    saId: 'Employee ID',
    attendanceDate: 'Attendance date',
    startWork: 'Start work',
    offWork: 'Off work',
    clockIn: 'Clock in',
    clockOut: 'Clock out',
    clockedIn: 'Clocked in',
    clockedOut: 'Clocked out',
    updateClockIn: 'Update Clock in',
    updateClockOut: 'Update Clock out',
    missingIn: 'Missing in',
    clockInSuccessfully: 'Clock in successfully',
    clockOutSuccessfully: 'Clock out successfully',
    holidays: 'Holidays',
    workingDay: 'Working Day',
    isInRange: 'Entered the clock in or out range',
    isNotInRange: 'Not in clock in or out range',
    deviceIdNotAvailable: 'There is a problem with the device ID, please contact the IT administrator',
    // 新增的考勤相关文本
    attendanceRules: 'Attendance Rules',
    fixedShifts: 'Fixed shifts',
    flexibleShifts: 'Flexible shifts',
    startWorkTime: 'Start work {time}',
    finishWorkTime: 'Finish work {time}',
    flexibleStart: 'Earliest start work {timeRange}',
    flexibleFinish: 'Earliest finish work {time}',
    normal: 'Normal',
    abnormalNoClockIn: 'Abnormal - No clock in',
    abnormalLateClockIn: 'Abnormal - Late clock in',
    abnormalEarlyClockOut: 'Abnormal - Early clock out',
    nextDay: 'Next day',
    // 统计页面相关文本
    lateArrival: 'Late arrival',
    leavingEarly: 'Leaving early',
    notClockingIn: 'Not clocking in',
    clockInTime: 'Clock in time:',
    clockOutTime: 'Clock out time:',
    noClockIn: 'No clock in',
    noClockOut: 'No clock out',
    noClockInRecord: 'No clock in record',
    noNeedToClock: 'No need to clock',
    attendanceGroupNotConfigured: 'Attendance group not configured',
    pleaseClockInFirst: 'Please clock in first',
    handling: 'Handling',
    clockedInUnderReview: 'Clocked in under review',
    // 补卡相关文案
    fixClockIn: 'Handling Exceptions',
    fixClockInType: 'Fix clock in type',
    reason: 'Reason',
    reasonPlaceholder: 'Please enter the reason for fixing clock in',
    reasonRequired: 'Please fill in Reason',
    reasonMaxLength: 'Reason cannot exceed 200 characters',
    confirm: 'Confirm',
    cancel: 'Cancel',
    fixClockInSuccess: 'Fix clock in submitted successfully',
    underReview: 'Under review',
    reviewSuccess: 'Review successful',
    reviewFailed: 'Review failed',
    approvalPassed: 'Approval passed',
    // 考勤规则弹窗文案
    attendanceRulesTitle: 'Attendance Rules',
    attendanceRulesContent: `No card replacement is allowed after missing card; Punching card in PC/Laptop is not allowed; if you are on the list of employees working from home,you can punch in outside.`,
    close: 'Close',
  },
  title: {
    attendance: 'Attendance',
    statistics: 'Statistics',
    basicInfo: 'Basic Information',
    identityDetails: 'Identity Details',
    contactDetails: 'Contact Details',
    bankcardInfo: 'Add Bank Card',
    verifyIDPhoto: 'Verify ID Photo',
    faceVerification: 'Face Verification',
    repaymentDetailsList: 'Repayment details',
    voiceCollection: 'Voice Collection',
    completed: 'Completed',
    reviewFailed: 'Review Failed',
  },
  common: {
    OKay: 'OKay',
    Close: 'Close',
    securityGuarantee: 'security guarantee',
    Help: 'Help',
    Edit: 'Edit',
    Next: 'Next',
    footerNote: 'Note: Please fill in your real contactal information, fill in the false information will affect your onboarding!',
  },
  calendar: {
    year: 'year',
    month: 'month',
    day: 'day',
    Confirm: 'Confirm',
    // v-calendar locale settings
    locale: {
      id: 'en-US',
      firstDayOfWeek: 1,
      masks: {
        title: 'MMMM YYYY',
        weekdays: 'WW',
        navMonths: 'MMM',
        input: 'YYYY-MM-DD',
        dayPopover: 'WWW, MMM D, YYYY',
        data: ['L', 'YYYY-MM-DD', 'YYYY/MM/DD']
      }
    }
  },
  toastMsg: {
    isPending: 'The information you filled in is under review',
    recordingLess: 'Recording time is less than 5 seconds',
    noPermission: 'Please grant the necessary permissions first',
    loadResourceFailed: 'System is being upgraded, please try again later',
    networkError: 'Network error, please try again later',
    profileChanged: 'Your profile has been changed, please re-login',
    pleaseEnableGpsPermission: 'Please enable GPS permission',
    permissionRequestFailed: 'Failed to obtain permissions, please try again later'
  },
  login: {
    enterEmail: 'Please enter your email address',
    enterValidEmail: 'Please enter a valid email address',
    inputNote: 'Account(Your personal email to HR)',
    personalEmailTip: 'Please use the personal email account registered at the time of employment.',
    enterOTP: 'Please enter 4-digit OTP',
    otpSentTo: 'otp sent to',
    getOtpAgain: 'Get OTP again',
  },
  faceVerification: {
    scanningFaces: 'Scanning faces',
    keep: 'Keep your face in the middle of the screen',
    note: 'Note',
    notice: 'Notice',

    photograph: 'Photograph',
    noticeList: {
      0: 'Light brigh',
      1: 'No phone vibration',
      2: 'No other people',
      3: 'Don\'t cover faces',
    },
    noteTextList: {
      0: '1. Please keep the light bright',
      1: '2. Please don\'t let others do face verification',
      2: '3. Do not cover your face or wear a mask',
      3: '4. Please do not take photos of mobile phone screens',
    },
  },
  voiceCollection: {
    noticeTextList: {
      0: 'Please click the Start button and read the following text:',
      1: 'Note: Recordings must be 5s-10s in length.'
    },
    guarantee: "I guarantee that the information provided is true and valid!",
    recordBtn: {
      start: "Start",
      end: "End",
      reRecord: "Re-record"
    },
    invalidFileFormat: 'Invalid file format',
  },
  personInfo: {
    authTitle: 'Authorization prompt',
    startAuth: 'Start authorization',
    welcome: 'Welcome to {appName}',
    obtainPermission: 'We will obtain some of your phone permissions so that you can use the App services normally.',
    returnBtn: 'Return',
    phoneNumber: 'Phone Number',
    male: 'Male',
    female: 'Female',
    email: 'Email',
    dataSafe: 'Your data is 100% safe',
    dataSafe2: 'We do not share your information with anyone!',
    genderLower: 'gender',
    genderUpper: 'Gender',
    emailLower: 'employee\'s email ID',
    emailUpper: 'Employee\'s Email ID',
    birthLower: 'date of birth',
    birthUpper: 'Date of Birth',
    educationalLower: 'educational back ground',
    educationalUpper: 'Educational Back Ground',
    majorLower: 'education major',
    majorUpper: 'Education Major',
    addressLower: 'employee\'s contact address',
    addressUpper: 'Employee\'s Contact Address',
    maritalStatusLower: 'marital status',
    maritalStatusUpper: 'Marital Status',
    childrenNumLower: 'number of children',
    childrenNumUpper: 'Number of children',
    lastCompanyNameLower: 'last company name',
    lastCompanyNameUpper: 'Last Company Name',
    lastJobPositionLower: 'last job position',
    lastJobPositionUpper: 'Last Job Position',
    single: "Single",
    married: "Married",
    seperated: "Seperated",
    divorced: "Divorced",
    widowed: "Widowed",
    educationLevel: {
      secondary: "Secondary Education/High School Diploma",
      associate: "Associate Degree/Diploma",
      bachelor: "Bachelor's Degree",
      master: "Master Degree or above",
      others: "Others"
    },
    schoolLower: "school name",
    schoolUpper: "School Name",
  },
  identityInfo: {
    firstNameLower: 'first name',
    firstNameUpper: 'First Name',
    middleNameLower: 'middle name',
    middleNameUpper: 'Middle Name',
    lastNameLower: 'last name',
    lastNameUpper: 'Last Name',
    idTypeLower: 'id type',
    idTypeUpper: 'ID Type',
    idNumberLower: 'id number',
    idNumberUpper: 'ID Number',
    idCardFrontPhotoLower: 'front side of ID card',
    idCardFrontPhotoUpper: 'Front side of ID card',
    idCardBackPhotoLower: 'back side of ID card',
    idCardBackPhotoUpper: 'Back side of ID card(Optional)',
    hasIdCardLower: 'have an ID card',
    hasIdCardUpper: 'Have an ID Card?(NIDA)',
    yes: 'Yes',
    no: 'No',
    pleaseUploadImage: 'please upload image file',
    fileSizeCannotExceed: 'file size cannot exceed {size}MB',
    compressImageError: 'compress image error: {error}',
    formatTips: 'Recommended to use jpg, jpeg, or png files',
  },
  contactInfo: {
    emergencyContactRelationLower: 'emergency contact relation',
    emergencyContactRelationUpper: 'Emergency Contact Relation',
    emergencyContactNameLower: 'emergency contact name',
    emergencyContactNameUpper: 'Emergency Contact Name',
    emergencyContactPhoneLower: 'emergency contact phone number',
    emergencyContactPhoneUpper: 'Emergency Contact Phone Number',
    guarantorContactRelationLower: 'guarantor contact relation',
    guarantorContactRelationUpper: 'Guarantor Contact Relation',
    guarantorContactNameLower: 'guarantor contact name',
    guarantorContactNameUpper: 'Guarantor Contact Name',
    guarantorContactPhoneLower: 'guarantor contact phone number',
    guarantorContactPhoneUpper: 'Guarantor Contact Phone Number',
  },
  bankcardInfo: {
    bankNameLower: 'bank name',
    bankNameUpper: 'Bank Name',
    bankBranchLower: 'bank branch',
    bankBranchUpper: 'Bank Branch(optional)',
    acctNumberLower: 'account number',
    acctNumberUpper: 'Account Number',
  },
  result: {
    failedTitle: 'Reminder for failed review',
    failedContent: 'Failure reason',
    successTitle: 'Welcome to join us!',
    successContent: 'Your information has been submitted, please wait for review',
    rejectedSystem: 'You are currently not eligible for employment',
    modifyNow: 'Modify now',
    gotoHome: 'Go to home',
  },
  testMsg: { // 校验提示语
    canNotEmpty: 'Can not be empty',
    pleseInputCorrect: 'Please input correct information',
    pleaseInputNumber: 'Please input the number',
    wrongCell: 'Wrong cell phone number, please correct',
    notFill: 'Please fill in {field} first',
    notSelect: 'Please select {field} first',
  },
};
