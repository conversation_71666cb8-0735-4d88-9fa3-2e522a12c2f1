const fs = require('fs');
const path = require('path');
const translateText = require('free-google-translator-api');

// 原始语言
const sourceLang = "en";
// 翻译的目标语言数组
const targetLangs = ["sw", "es", "fr", "zh"]; // 添加更多语言代码

// 不需要翻译的 key 数组
const excludeKeys = ["key1", "key2"]; // 自定义不需要翻译的 key

// 读取源语言文件
const sourceFilePath = path.join(__dirname, `${sourceLang}.js`);
const sourceData = require(sourceFilePath);

// 匹配占位符的正则表达式
const PLACEHOLDER_REGEX = /\{[^}]+\}/g;

// 异步翻译函数
async function translateFun(data, key, value, targetLang) {
  // 如果 key 在 excludeKeys 中，跳过翻译
  if (excludeKeys.includes(key)) {
    data[key] = value; // 直接保留原始值
    return true; // 跳过翻译
  }

  // 提取占位符
  const placeholders = value.match(PLACEHOLDER_REGEX) || [];

  // 将占位符替换为临时标记，避免被翻译
  let tempText = value;
  placeholders.forEach((placeholder, index) => {
    tempText = tempText.replace(placeholder, `__PLACEHOLDER_${index}__`);
  });

  try {
    // 翻译临时文本
    let translatedText = await translateText(tempText, sourceLang, targetLang);

    // 将临时标记恢复为占位符
    placeholders.forEach((placeholder, index) => {
      translatedText = translatedText.replace(`__PLACEHOLDER_${index}__`, placeholder);
    });

    // 更新数据（确保 data 是一个可变对象）
    data[key] = translatedText;
    return true; // 翻译成功
  } catch (error) {
    console.error(`Translation failed for key "${key}" in language "${targetLang}":`, error);
    return false; // 翻译失败
  }
}

// 递归翻译函数
async function translateObject(data, targetLang) {
  for (const key of Object.keys(data)) {
    const value = data[key];

    if (typeof value === 'string') {
      // 如果是字符串，调用 translateFun
      const success = await translateFun(data, key, value, targetLang);
      if (!success) {
        return false; // 如果翻译失败，提前退出
      }
    } else if (typeof value === 'object' && value !== null) {
      // 如果是对象，递归调用 translateObject
      const success = await translateObject(value, targetLang);
      if (!success) {
        return false; // 如果翻译失败，提前退出
      }
    }
  }
  return true; // 所有翻译成功
}

// 递归生成对象字符串
function generateObjectString(obj, indent = 2, level = 1) {
  const indentStr = ' '.repeat(indent * level);
  const entries = Object.entries(obj);

  return `{
${entries.map(([key, value]) => {
    if (typeof value === 'object' && value !== null) {
      return `${indentStr}${key}: ${generateObjectString(value, indent, level + 1)}`;
    } else {
      return `${indentStr}${key}: ${JSON.stringify(value)}`;
    }
  }).join(',\n')}
${' '.repeat(indent * (level - 1))}}`;
}

// 主函数：逐个语言翻译并生成文件
async function translateAndSaveFiles() {
  for (const targetLang of targetLangs) {
    console.log(`Starting translation for language: ${targetLang}`);

    // 深拷贝源数据，避免修改原始数据
    const dataCopy = JSON.parse(JSON.stringify(sourceData));

    // 调用翻译函数
    const success = await translateObject(dataCopy, targetLang);

    if (success) {
      // 生成目标文件内容
      const content = `module.exports = ${generateObjectString(dataCopy)};`;

      // 将翻译后的数据写入目标文件
      const targetFilePath = path.join(__dirname, `${targetLang}.js`);
      fs.writeFileSync(targetFilePath, content);
      console.log(`Translation results saved to ${targetLang}.js`);
    } else {
      console.error(`Translation failed for language ${targetLang}, skipping file write.`);
    }
  }
}

// 执行主函数
translateAndSaveFiles().catch(error => {
  console.error('Error during translation process:', error);
});
