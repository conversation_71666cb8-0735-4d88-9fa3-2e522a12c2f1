module.exports = {
  home: {
    bannerText: "Unda timu bora kuanzia {appName}",
    functionMenu: "<PERSON>yu ya Kazi",
    attendance: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    myProfile: "<PERSON><PERSON><PERSON> Wang<PERSON>",
    todoItems: "Vitu vya kufanya",
    supplement: "Ongeza maelezo ya kibinafsi",
    supplementModify: "Kuna tatizo na maelezo yako ya ajira, tafadhali angalia",
    deadline: "Tarehe ya mwisho",
    clockIn: "<PERSON>ing<PERSON>",
    clockOut: "<PERSON>tok<PERSON>",
    toComplete: "kukamilisha",
    rejectedSystem: "Hujatimiza masharti ya kuajiriwa kwa sasa",
    logout: "Ondoka",
    return: '<PERSON><PERSON>'
  },
  logout: {
    confirmText: "Thibitisha kuondoka?",
    confirm: "Thibitisha",
    cancel: "<PERSON><PERSON><PERSON>"
  },
  attendance: {
    saId: "Kitambulisho cha mfanyakazi",
    attendanceDate: "Tarehe ya mahudhurio",
    startWork: "Anza kazi",
    offWork: "<PERSON>cha kazi",
    clockIn: "<PERSON>ing<PERSON>",
    clockOut: "<PERSON>tok<PERSON>",
    clockedIn: "Umeingia",
    clockedOut: "Umetoka",
    updateClockIn: "Sasisha Saa ndani",
    updateClockOut: "Sasisha Saa nje",
    missingIn: "Hujaingia",
    clockInSuccessfully: "umeingia kikamilifu",
    clockOutSuccessfully: "umetoka kikamilifu",
    holidays: "Likizo",
    workingDay: "Siku ya Kazi",
    isInRange: "uko ndani ya masafa",
    isNotInRange: "uko nje ya masafa",
    deviceIdNotAvailable: "Kuna tatizo na kitambulisho cha kifaa, tafadhali wasiliana na msimamizi wa TEHAMA",
    // 新增的考勤相关文本
    attendanceRules: "Sheria za Mahudhurio",
    fixedShifts: "Zamu za kudumu",
    flexibleShifts: "Zamu za kubadilika",
    startWorkTime: "Anza kazi {time}",
    finishWorkTime: "Maliza kazi {time}",
    flexibleStart: "Mwanzo wa mapema wa kazi {timeRange}",
    flexibleFinish: "Mwisho wa mapema wa kazi {time}",
    normal: "Kawaida",
    abnormalNoClockIn: "Isiyo ya kawaida - Hakuna kuingia",
    abnormalLateClockIn: "Isiyo ya kawaida - Kuingia kuchelewa",
    abnormalEarlyClockOut: "Isiyo ya kawaida - Kutoka mapema",
    nextDay: "Siku ijayo",
    // 统计页面相关文本
    lateArrival: "Kufika kuchelewa",
    leavingEarly: "Kuondoka mapema",
    notClockingIn: "Hakuna kuingia",
    clockInTime: "Muda wa kuingia:",
    clockOutTime: "Muda wa kutoka:",
    noClockIn: "Hakuna kuingia",
    noClockOut: "Hakuna kutoka",
    noNeedToClock: "Hakuna haja ya kuingia",
    noClockInRecord: "Hakuna rekodi ya kuingia",
    attendanceGroupNotConfigured: "Kikundi cha mahudhurio hakijasanidiwa",
    pleaseClockInFirst: "Tafadhali ingia kwanza",
    handling: "Inashughulikiwa",
    clockedInUnderReview: "Kuingia kinakaguliwa",
    // 补卡相关文案
    fixClockIn: "Kushughulikia Vighairi",
    fixClockInType: "Aina ya marekebisho ya kuingia",
    reason: "Sababu",
    reasonPlaceholder: "Tafadhali ingiza sababu ya marekebisho ya kuingia",
    reasonRequired: "Tafadhali jaza sababu",
    reasonMaxLength: "Sababu haiwezi kuzidi herufi 200",
    confirm: "Thibitisha",
    cancel: "Ghairi",
    fixClockInSuccess: "Marekebisho ya kuingia yamewasilishwa kwa ufanisi",
    underReview: "Inakaguliwa",
    reviewSuccess: "Ukaguzi umefanikiwa",
    reviewFailed: "Ukaguzi umeshindwa",
    approvalPassed: "Idhini imepita",
    // 考勤规则弹窗文案
    attendanceRulesTitle: "Sheria za Mahudhurio",
    attendanceRulesContent: `Aina ya Mahudhurio: Mahudhurio ya Kudumu

Masaa ya Kazi: 09:00 - 18:00

Muda wa Mapumziko: 12:00 - 13:00

Muda wa Kazi: Masaa 8

Eneo la Kuingia: WiFi ya ofisi inahitajika

Kumbuka: Tafadhali hakikisha umeunganishwa kwenye mtandao wa WiFi wa ofisi wakati wa kuingia au kutoka.`,
    close: "Funga",
  },
  title: {
    attendance: "Mahudhurio",
    statistics: "Takwimu",
    basicInfo: "Taarifa za Msingi",
    identityDetails: "Maelezo ya Utambulisho",
    contactDetails: "Maelezo ya Mawasiliano",
    bankcardInfo: "Taarifa za kadi ya banki",
    verifyIDPhoto: "Thibitisha Picha ya Kitambulisho",
    faceVerification: "Uthibitisho wa Uso",
    repaymentDetailsList: "Maelezo ya ulipaji",
    voiceCollection: "Ukusanyaji wa Sauti",
    completed: "Imekamilika",
    reviewFailed: "Uhakiki Umeshindwa"
  },
  common: {
    OKay: "Sawa",
    Close: "Funga",
    securityGuarantee: "dhamana ya usalama",
    Help: "Msaada",
    Edit: "Rekebisha",
    Next: "Inayofuata",
    footerNote: "Kumbuka: Tafadhali jaza maelezo yako halisi ya mawasiliano, jaza maelezo ya uwongo yataathiri upandaji wako!"
  },
  calendar: {
    year: "mwaka",
    month: "mwezi",
    day: "siku",
    Confirm: "Thibitisha",
    // v-calendar locale settings - 使用英语格式作为兜底
    locale: {
      id: 'sw',
      firstDayOfWeek: 1,
      masks: {
        title: 'MMMM YYYY',
        weekdays: 'WW',
        navMonths: 'MMM',
        input: 'YYYY-MM-DD',
        dayPopover: 'WWW, MMM D, YYYY', // 使用英语格式
        data: ['L', 'YYYY-MM-DD', 'YYYY/MM/DD']
      }
    }
  },
  toastMsg: {
    isPending: "Taarifa uliyojaza inakaguliwa",
    recordingLess: "Muda wa kurekodi ni chini ya sekunde 5",
    noPermission: "Tafadhali toa ruhusa zinazohitajika kwanza",
    loadResourceFailed: "Mfumo unasasishwa, tafadhali jaribu tena baadaye",
    networkError: "Hitilafu ya mtandao, tafadhali jaribu tena baadaye",
    profileChanged: "Wasifu wako umebadilishwa, tafadhali ingia tena",
    pleaseEnableGpsPermission: "Tafadhali wezesha ruhusa ya GPS",
    permissionRequestFailed: "Imeshindwa kupata ruhusa, tafadhali jaribu tena baadaye"
  },
  login: {
    enterEmail: "Tafadhali ingiza barua pepe yako",
    enterValidEmail: "Tafadhali weka barua pepe halisi",
    inputNote: "Akaunti (Barua pepe yako ya kibinafsi kwa HR)",
    personalEmailTip: "Tafadhali tumia akaunti ya barua pepe ya kibinafsi iliyosajiliwa wakati wa ajira.",
    enterOTP: "Tafadhali weka OTP yenye tarakimu 4",
    otpSentTo: "otp imetumwa kwa",
    getOtpAgain: "Pata OTP tena"
  },
  faceVerification: {
    scanningFaces: "Inachanganua nyuso",
    keep: "Weka uso wako katikati ya skrini",
    note: "Kumbuka",
    notice: "Taarifa",
    photograph: "Picha",
    noticeList: {
      0: "Mwanga mkali",
      1: "Hakuna mtetemo wa simu",
      2: "Hakuna watu wengine",
      3: "Usifunike nyuso"
    },
    noteTextList: {
      0: "1. Tafadhali weka mwangaza",
      1: "2. Tafadhali usiruhusu wengine kufanya uthibitishaji wa uso",
      2: "3. Usifunike uso wako au kuvaa barakoa",
      3: "4. Tafadhali usichukue picha za skrini za simu ya mkononi"
    }
  },
  voiceCollection: {
    noticeTextList: {
      0: 'Tafadhali bonyeza kitufe cha Kuanza na usome maandishi yafuatayo:',
      1: 'Kumbuka: Rekodi lazima ziwe na urefu wa sekunde 5-10.'
    },
    guarantee: "Ninahakikisha kuwa taarifa zilizotolewa ni za kweli na halali!",
    recordBtn: {
      start: "Anza Kurekodi",
      end: "Maliza Kurekodi",
      reRecord: "Rekodi Tena"
    },
    invalidFileFormat: 'Muundo wa faili si sahihi',
  },
  personInfo: {
    authTitle: "Kidokezo cha uidhinishaji",
    startAuth: "Anza idhini",
    welcome: "Karibu {appName}",
    obtainPermission: "Tutapata baadhi ya vibali vya simu yako ili uweze kutumia huduma za Programu kama kawaida.",
    returnBtn: "Rudi",
    phoneNumber: "Nambari ya Simu",
    male: "Mwanaume",
    female: "Mwanamke",
    email: "Barua pepe",
    dataSafe: "Data yako iko salama 100%.",
    dataSafe2: "Hatushiriki maelezo yako na mtu yeyote!",
    genderLower: "jinsia",
    genderUpper: "Jinsia",
    emailLower: "kitambulisho cha barua pepe cha mfanyakazi",
    emailUpper: "Kitambulisho cha Barua Pepe cha Mfanyakazi",
    birthLower: "tarehe ya kuzaliwa",
    birthUpper: "Tarehe ya Kuzaliwa",
    educationalLower: "msingi wa elimu",
    educationalUpper: "Msingi wa Elimu",
    majorLower: "elimu mkuu",
    majorUpper: "Elimu Mkuu",
    addressLower: "anwani ya mawasiliano ya mfanyakazi",
    addressUpper: "Anwani ya Mawasiliano ya Mfanyakazi",
    maritalStatusLower: "hali ya ndoa",
    maritalStatusUpper: "Hali ya Ndoa",
    childrenNumLower: "idadi ya watoto",
    childrenNumUpper: "Idadi ya watoto",
    lastCompanyNameLower: "jina la kampuni ya mwisho",
    lastCompanyNameUpper: "Jina la Kampuni ya Mwisho",
    lastJobPositionLower: "nafasi ya mwisho ya kazi",
    lastJobPositionUpper: "Nafasi ya Kazi ya Mwisho",
    single: "sijaolewa",
    married: "nimeolewa",
    seperated: "Tumetengana",
    divorced: "talaka",
    widowed: "Mjane",
    educationLevel: {
      secondary: "Diploma ya Elimu ya Sekondari/Shule ya Sekondari",
      associate: "Shahada/Diploma",
      bachelor: "Shahada ya kwanza",
      master: "Shahada ya Uzamili au zaidi",
      others: "Wengine"
    },
    schoolLower: "jina la shule",
    schoolUpper: "Jina la Shule"
  },
  identityInfo: {
    firstNameLower: "jina la kwanza",
    firstNameUpper: "Jina la kwanza",
    middleNameLower: "jina la kati",
    middleNameUpper: "Jina la Kati",
    lastNameLower: "jina la mwisho",
    lastNameUpper: "Jina la mwisho",
    idTypeLower: "aina ya kitambulisho",
    idTypeUpper: "Aina ya kitambulisho",
    idNumberLower: "nambari ya kitambulisho",
    idNumberUpper: "Nambari ya kitambulisho",
    idCardFrontPhotoLower: "upande wa mbele wa kitambulisho",
    idCardFrontPhotoUpper: "Upande wa mbele wa kitambulisho",
    idCardBackPhotoLower: "upande wa nyuma wa kitambulisho",
    idCardBackPhotoUpper: "Upande wa nyuma wa kadi ya kitambulisho (Si lazima)",
    hasIdCardLower: 'ana kitambulisho',
    hasIdCardUpper: 'Ana Kitambulisho?(NIDA)',
    yes: 'Ndiyo',
    no: 'Hapana',
    pleaseUploadImage: 'tafadhali pakia faili la picha',
    fileSizeCannotExceed: 'ukubwa wa faili hauwezi kuzidi {size}MB',
    compressImageError: 'hitilafu ya kubana picha: {error}',
    formatTips: 'Inapendekezwa kutumia faili za jpg, jpeg, au png',
  },
  contactInfo: {
    emergencyContactRelationLower: "mawasiliano ya dharura",
    emergencyContactRelationUpper: "Uhusiano wa Mawasiliano ya Dharura",
    emergencyContactNameLower: "jina la mawasiliano ya dharura",
    emergencyContactNameUpper: "Jina la Anwani ya Dharura",
    emergencyContactPhoneLower: "nambari ya simu ya dharura",
    emergencyContactPhoneUpper: "Nambari ya Simu ya Mawasiliano ya Dharura",
    guarantorContactRelationLower: "uhusiano wa mawasiliano ya mdhamini",
    guarantorContactRelationUpper: "Uhusiano wa Mawasiliano wa Mdhamini",
    guarantorContactNameLower: "jina la mawasiliano ya mdhamini",
    guarantorContactNameUpper: "Jina la Anwani ya Mdhamini",
    guarantorContactPhoneLower: "nambari ya simu ya mdhamini",
    guarantorContactPhoneUpper: "Nambari ya Simu ya Mdhamini"
  },
  bankcardInfo: {
    bankNameLower: "jina la benki",
    bankNameUpper: "Jina la Benki",
    bankBranchLower: "tawi la benki",
    bankBranchUpper: "Tawi la Benki (hiari)",
    acctNumberLower: "nambari ya akaunti",
    acctNumberUpper: "Nambari ya Akaunti"
  },
  result: {
    failedTitle: "Kikumbusho cha ukaguzi usiofanikiwa",
    failedContent: "Sababu ya kushindwa",
    successTitle: "Karibu ujiunge nasi!",
    successContent: "Maelezo yako yamewasilishwa, tafadhali subiri ukaguzi",
    rejectedSystem: "Hujatimiza masharti ya kuajiriwa kwa sasa",
    modifyNow: "Rekebisha sasa",
    gotoHome: "Nenda nyumbani"
  },
  testMsg: {
    canNotEmpty: "Haiwezi kuwa tupu",
    pleseInputCorrect: "Tafadhali weka taarifa sahihi",
    pleaseInputNumber: "Tafadhali weka nambari",
    wrongCell: "Nambari ya simu isiyo sahihi, tafadhali sahihisha",
    notFill: "Tafadhali jaza {field} kwanza",
    notSelect: "Tafadhali chagua {field} kwanza"
  }
};
