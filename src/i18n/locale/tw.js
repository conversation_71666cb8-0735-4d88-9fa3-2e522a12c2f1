module.exports = {
  home: {
    bannerText: 'Yɛ kuw a ɛyɛ adwuma yiye a efi ase fi {appName}',
    functionMenu: 'Dwu<PERSON>ie Menu',
    attendance: 'Ne kɔ',
    myProfile: 'Me Profile',
    todoItems: 'Nneɛma a ɛsɛ sɛ wɔyɛ',
    supplement: 'Fa w\'ankasa wo ho nsɛm ka ho',
    supplementModify: 'Ɔhaw bi wɔ w\'adwuma ho nsɛm mu, yɛsrɛ wo hwɛ',
    deadline: 'Awieeɛ da',
    clockIn: 'Dɔn wɔ mu',
    clockOut: 'Dɔn no fi adi',
    toComplete: 'Sɛ wobɛwie',
    rejectedSystem: 'M<PERSON>rem<PERSON><PERSON> womfata sɛ wonya adwuma',
    logout: 'Fi wo ho fi mu',
    return: 'San'
  },
  logout: {
    confirmText: 'Si so dua sɛ logout?',
    confirm: 'Si pi',
    cancel: 'Twam'
  },
  attendance: {
    saId: 'Adwumayɛfo ID',
    attendanceDate: 'Da a wɔde bɛba',
    startWork: 'Fi ase yɛ adwuma',
    offWork: 'Off adwuma',
    clockIn: 'Dɔn wɔ mu',
    clockOut: 'Dɔn no fi adi',
    clockedIn: 'Wɔde dɔn a wɔde ahyɛ mu',
    clockedOut: 'Wɔabɔ dɔn no',
    updateClockIn: 'Update Clock wɔ mu',
    updateClockOut: 'Update Clock no fi adi',
    missingIn: 'Wɔayera wɔ',
    clockInSuccessfully: 'Clock in mu yiye',
    clockOutSuccessfully: 'Clock out yiye',
    holidays: 'Nnapɔnna',
    workingDay: 'Adwuma Da',
    isInRange: 'Ɔhyɛn dɔn no mu wɔ anaasɛ fi mu',
    isNotInRange: 'Ɛnyɛ dɔn a ɛkɔ mu anaa akyi kwan mu',
    deviceIdNotAvailable: 'Ɔhaw bi wɔ device ID no ho, yɛsrɛ wo, di IT sohwɛfoɔ no ho nkɔmmɔ',
    // 新增的考勤相关文本
    attendanceRules: 'Ne kɔ Mmara',
    fixedShifts: 'Adwuma bere a ɛyɛ daa',
    flexibleShifts: 'Adwuma bere a ɛsesa',
    startWorkTime: 'Fi ase yɛ adwuma {time}',
    finishWorkTime: 'Wie adwuma {time}',
    flexibleStart: 'Ntɛm mfiase adwuma {timeRange}',
    flexibleFinish: 'Ntɛm awieeɛ adwuma {time}',
    normal: 'Ɛyɛ daa',
    abnormalNoClockIn: 'Ɛnyɛ daa - Clock in biara nni hɔ',
    abnormalLateClockIn: 'Ɛnyɛ daa - Clock in a ɛkyɛe',
    abnormalEarlyClockOut: 'Ɛnyɛ daa - Clock out ntɛm',
    nextDay: 'Ɔkyena',
    // 统计页面相关文本
    lateArrival: 'Ɔba akyiri',
    leavingEarly: 'Ɔkɔ ntɛm',
    notClockingIn: 'Clock in biara nni hɔ',
    clockInTime: 'Clock in bere:',
    clockOutTime: 'Clock out bere:',
    noClockIn: 'Clock in biara nni hɔ',
    noClockOut: 'Clock out biara nni hɔ',
    noClockInRecord: 'Clock in record biara nni hɔ',
    noNeedToClock: 'Ɛho nhia sɛ wɔbɔ dɔn',
    attendanceGroupNotConfigured: 'Wɔnhyehyɛɛ attendance kuw no',
    pleaseClockInFirst: 'Yɛsrɛ wo dɔn wɔ mu kane',
    handling: 'Wɔreyɛ ho adwuma',
    clockedInUnderReview: 'Clock in no wɔrehwɛ mu',
    // 补卡相关文案
    fixClockIn: 'Exception ahorow ho dwumadi',
    fixClockInType: 'Clock in teɛteɛ type',
    reason: 'Ntease',
    reasonPlaceholder: 'Yɛsrɛ wo kyerɛw ntease a ɛma woteɛteɛ clock in',
    reasonRequired: 'Yɛsrɛ wo hyɛ Ntease no ma',
    reasonMaxLength: 'Ntease no ntumi ntra nsɛmfua 200',
    confirm: 'Si pi',
    cancel: 'Twam',
    fixClockInSuccess: 'Clock in teɛteɛ no akɔ kɔ yiye',
    underReview: 'Wɔrehwɛ mu',
    reviewSuccess: 'Nhwehwɛmu no ayɛ yiye',
    reviewFailed: 'Nhwehwɛmu no adi nkogu',
    approvalPassed: 'Mpene no afa mu',
    // 考勤规则弹窗文案
    attendanceRulesTitle: 'Ne kɔ Mmara',
    attendanceRulesContent: `Ne kɔ Type: Ne kɔ a Ɛyɛ Daa

Adwuma Bere: 09:00 - 18:00

Ahomegye Bere: 12:00 - 13:00

Adwuma Tenten: Nnɔnhwerew 8

Clock In Range: Adwumayɛbea WiFi ho hia

Hyɛ no nsow: Yɛsrɛ sɛ hwɛ sɛ woaka adwumayɛbea WiFi network no ho bere a woreclock in anaa clock out.`,
    close: 'To mu',
  },
  title: {
    attendance: 'Ne kɔ',
    statistics: 'Akontaabu',
    basicInfo: 'Nsɛm a Ɛho Hia',
    identityDetails: 'Nnipa a Wɔyɛ Wɔn Ho Nsɛm',
    contactDetails: 'Nkitahodi ho nsɛm',
    bankcardInfo: 'Fa Sikakorabea Kaad ka ho',
    verifyIDPhoto: 'Hwɛ sɛ ID Mfonini no yɛ nokware',
    faceVerification: 'Anim a Wɔde Di Dwuma',
    repaymentDetailsList: 'Nsɛm a ɛfa sika a wotua ho',
    voiceCollection: 'Nne a Wɔaboaboa Ano',
    completed: 'Wɔawie',
    reviewFailed: 'Nhwehwɛmu no adi nkogu'
  },
  common: {
    OKay: 'Yoo',
    Close: 'To mu',
    securityGuarantee: 'ahobammɔ ho bɔhyɛ',
    Help: 'Boa',
    Edit: 'Sa mu',
    Next: 'Deɛ ɛdi hɔ',
    footerNote: 'Hyɛ no nsow: Yɛsrɛ sɛ hyehyɛ wo contactal information ankasa, hyehyɛ atoro nsɛm no bɛka wo onboarding!'
  },
  calendar: {
    year: 'afe',
    month: 'bosome',
    day: 'da',
    Confirm: 'Si pi',
    // v-calendar locale settings - 使用英语格式作为兜底
    locale: {
      id: 'tw',
      firstDayOfWeek: 1,
      masks: {
        title: 'MMMM YYYY',
        weekdays: 'WW',
        navMonths: 'MMM',
        input: 'YYYY-MM-DD',
        dayPopover: 'WWW, MMM D, YYYY', // 使用英语格式
        data: ['L', 'YYYY-MM-DD', 'YYYY/MM/DD']
      }
    }
  },
  toastMsg: {
    isPending: 'Wɔrehwɛ nsɛm a wohyɛɛ no ​​ma no mu',
    recordingLess: 'Bere a wɔde kyere nsɛm gu kasɛt so no nnu sikani 5',
    noPermission: 'Yɛsrɛ sɛ di kan ma kwan a ɛho hia no',
    loadResourceFailed: 'Wɔreyɛ system no foforo, yɛsrɛ wo san sɔ hwɛ akyiri yi',
    networkError: 'Network error, yɛsrɛ wo san sɔ hwɛ akyiri yi',
    profileChanged: 'Wɔasesa wo profile, yɛsrɛ wo san kɔ mu',
    pleaseEnableGpsPermission: 'Yɛsrɛ sɛ ma GPS kwan no',
    permissionRequestFailed: 'Antumi anya kwan no, yɛsrɛ wo san sɔ hwɛ akyiri yi'
  },
  login: {
    enterEmail: 'Yɛsrɛ sɛ kyerɛw wo email address no',
    enterValidEmail: 'Yɛsrɛ sɛ kyerɛw email address a ɛfata',
    inputNote: 'Account(W\'ankasa email a wode kɔma HR)',
    personalEmailTip: 'Yɛsrɛ sɛ fa w\'ankasa email account a wɔkyerɛw wɔ adwuma no mfiase no di dwuma.',
    enterOTP: 'Yɛsrɛ wo kyerɛw OTP a ɛwɔ digit 4',
    otpSentTo: 'otp a wɔde kɔmaa',
    getOtpAgain: 'Nya OTP bio'
  },
  faceVerification: {
    scanningFaces: 'Scanning anim',
    keep: 'Ma w\'anim nkɔ screen no mfinimfini',
    note: 'Hyɛ nso',
    notice: 'Nkaebɔ',
    photograph: 'Mfonini no',
    noticeList: {
      0: 'Hann brigh',
      1: 'Fone no wosow biara nni hɔ',
      2: 'Nnipa foforo biara nni hɔ',
      3: 'Mma nnkata w\'anim'
    },
    noteTextList: {
      0: 'Yɛsrɛ sɛ momma hann no nhyerɛn',
      1: 'Yɛsrɛ sɛ mma afoforo nyɛ face verification',
      2: 'Nkata w\'anim anaasɛ nhyɛ akataso',
      3: 'Yɛsrɛ sɛ ntwe mfonini wɔ telefon a wokura kyin screen so'
    }
  },
  voiceCollection: {
    noticeTextList: {
      0: 'Yɛsrɛ wo mia Start button no na kenkan nsɛm a edidi so yi:',
      1: 'Hyɛ no nsow: Ɛsɛ sɛ nsɛm a wɔakyere agu hama so no tenten yɛ 5s-10s.'
    },
    guarantee: 'Mehyɛ bɔ sɛ nsɛm a wɔde ama no yɛ nokware na ɛyɛ nokware!',
    recordBtn: {
      start: 'Hyɛ aseɛ',
      end: 'Awieeɛ',
      reRecord: 'San kyerɛw nsɛm bio'
    },
    invalidFileFormat: 'Fael no nhyehyɛe a ɛnteɛ'
  },
  personInfo: {
    authTitle: 'Tumi krataa ho nsɛm a wɔka kyerɛ',
    startAuth: 'Fi ase ma tumi krataa',
    welcome: 'Yɛma wo akwaaba ba {appName} so.',
    obtainPermission: 'Yɛbɛnya wo fon ho kwan no bi sɛdeɛ ɛbɛyɛ a wobɛtumi de App services no adi dwuma sɛdeɛ ɛteɛ.',
    returnBtn: 'San',
    phoneNumber: 'Telefon Nnɔmba',
    male: 'Barima',
    female: 'Ɔbaa koko',
    email: 'Email a wɔde mena',
    dataSafe: 'Wo data no yɛ 100% ahobammɔ',
    dataSafe2: 'Yɛmfa wo ho nsɛm nkyerɛ obiara!',
    genderLower: 'bɔbea',
    genderUpper: 'Bɔbea',
    emailLower: 'odwumayɛni no email ID',
    emailUpper: 'Adwumayɛfoɔ Email ID',
    birthLower: 'da a wɔwoo no',
    birthUpper: 'Da a Wɔwoo no',
    educationalLower: 'nhomasua akyi asɛm',
    educationalUpper: 'Nhomasua Akyi Gyinabea',
    majorLower: 'nhomasua mu ade titiriw',
    majorUpper: 'Nhomasua Major',
    addressLower: 'odwumayɛni no address a ɔde bɛdi nkitaho',
    addressUpper: 'Adwumayɛfo no Nkitahodi Address',
    maritalStatusLower: 'aware mu tebea',
    maritalStatusUpper: 'Aware Gyinabea',
    childrenNumLower: 'mmofra dodow',
    childrenNumUpper: 'Mmofra dodow',
    lastCompanyNameLower: 'adwumakuw no din a etwa to',
    lastCompanyNameUpper: 'Adwumakuw no Din a Etwa To',
    lastJobPositionLower: 'adwuma mu dibea a etwa to',
    lastJobPositionUpper: 'Adwuma Dibea a Etwa To',
    single: 'Ankonam',
    married: 'Aware',
    seperated: 'Wɔatetew mu',
    divorced: 'Wɔagyae aware',
    widowed: 'Okunafo',
    educationLevel: {
      secondary: 'Ntoaso Nhomasua/Ntoaso Sukuu Diploma',
      associate: 'Abodin krataa/Diploma a ɛka bom',
      bachelor: 'Bachelor\'s Abodin krataa',
      master: 'Master Degree anaa nea ɛboro saa',
      others: 'Afoforo nso'
    },
    schoolLower: 'sukuu din',
    schoolUpper: 'Sukuu Din'
  },
  identityInfo: {
    firstNameLower: 'fie din',
    firstNameUpper: 'Fie din',
    middleNameLower: 'mfinimfini din',
    middleNameUpper: 'Mfinimfini Edin',
    lastNameLower: 'abusuadin',
    lastNameUpper: 'Abusuadin',
    idTypeLower: 'id type',
    idTypeUpper: 'ID Type',
    idNumberLower: 'id nɔma',
    idNumberUpper: 'ID Nnɔmba',
    idCardFrontPhotoLower: 'ID krataa no anim fã',
    idCardFrontPhotoUpper: 'ID krataa no anim fã',
    idCardBackPhotoLower: 'ID krataa no akyi fã',
    idCardBackPhotoUpper: 'Akyi fã a ID card(Optional)',
    hasIdCardLower: 'wɔwɔ ID krataa',
    hasIdCardUpper: 'Wɔwɔ ID Krataa?(NIDA)',
    yes: 'Aane',
    no: 'Daabi',
    pleaseUploadImage: 'yɛsrɛ sɛ fa mfonini fael no to so',
    fileSizeCannotExceed: 'fael kɛseɛ ntumi ntra {size}MB',
    compressImageError: 'compress mfonini mfomso: {error}',
    formatTips: 'Wɔkamfo kyerɛ sɛ fa jpg, jpeg, anaa png fael di dwuma',
  },
  contactInfo: {
    emergencyContactRelationLower: 'ntɛmpɛ nkitahodi abusuabɔ',
    emergencyContactRelationUpper: 'Nkitahodi a Ɛho Hia Ntɛmntɛm',
    emergencyContactNameLower: 'ntɛmpɛ nkitahodi din',
    emergencyContactNameUpper: 'Edin a Wɔde Di Dwuma a Ɛho Hia Ntɛmntɛm',
    emergencyContactPhoneLower: 'telefon nɔma a wɔde di nkitaho ntɛm ara',
    emergencyContactPhoneUpper: 'Telefon Nɔma a Wɔde Di Dwuma a Egye Ntɛmntɛm',
    guarantorContactRelationLower: 'guarantor nkitahodi abusuabɔ',
    guarantorContactRelationUpper: 'Guarantor Nkitahodi Abusuabɔ',
    guarantorContactNameLower: 'guarantor nkitahodi din',
    guarantorContactNameUpper: 'Guarantor Nkitahodi Edin',
    guarantorContactPhoneLower: 'guarantor contact telefon nɔma',
    guarantorContactPhoneUpper: 'Guarantor Nkitahodi Telefon Nɔma'
  },
  bankcardInfo: {
    bankNameLower: 'sikakorabea din',
    bankNameUpper: 'Sikakorabea Din',
    bankBranchLower: 'sikakorabea baa dwumadibea',
    bankBranchUpper: 'Sikakorabea Baa Dwumadibea(wɔpɛ)',
    acctNumberLower: 'akontaabu nɔma',
    acctNumberUpper: 'Akontaabu Nnɔmba'
  },
  result: {
    failedTitle: 'Nkaebɔ ma nhwehwɛmu a entumi nyɛ yiye',
    failedContent: 'Ntease a ɛfa huammɔdi ho',
    successTitle: 'Yɛma wo akwaaba sɛ wobɛka yɛn ho!',
    successContent: 'Wɔde wo nsɛm no amena, yɛsrɛ wo twɛn ma wɔnsan nhwɛ mu',
    rejectedSystem: 'Mprempren womfata sɛ wonya adwuma',
    modifyNow: 'Sesa mprempren',
    gotoHome: 'Kɔ fie'
  },
  testMsg: {
    canNotEmpty: 'Entumi nyɛ nea hwee nni mu',
    pleseInputCorrect: 'Yɛsrɛ sɛ fa nsɛm a ɛteɛ hyɛ mu',
    pleaseInputNumber: 'Yɛsrɛ sɛ fa nɔma no hyɛ mu',
    wrongCell: 'Cell phone nɔma a ɛnteɛ, yɛsrɛ sɛ teɛteɛ',
    notFill: 'Yɛsrɛ wo, di kan hyɛ {field} no ma',
    notSelect: 'Yɛsrɛ wo, di kan paw {field}'
  }
};
