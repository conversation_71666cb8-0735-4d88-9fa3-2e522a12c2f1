module.exports = {
  home: {
    bannerText: "从{appName}开始建立一支高效的团队",
    functionMenu: "功能菜单",
    attendance: "出席率",
    myProfile: "我的个人资料",
    todoItems: "待办事项",
    supplement: "补充个人信息",
    supplementModify: "您的就业信息有问题，请检查",
    deadline: "最后期限",
    clockIn: "打卡上班",
    clockOut: "打卡下班",
    toComplete: "完成",
    rejectedSystem: "您目前不符合就业资格",
    logout: "退出"
  },
  logout: {
    confirmText: "确认退出？",
    confirm: "确认",
    cancel: "取消"
  },
  attendance: {
    saId: "员工工号",
    attendanceDate: "出席日期",
    startWork: "动手",
    offWork: "下班了",
    clockIn: "打卡上班",
    clockOut: "打卡下班",
    clockedIn: "已打卡上班",
    clockedOut: "打卡下班",
    updateClockIn: "更新打卡时间",
    updateClockOut: "更新下班时间",
    missingIn: "失踪于",
    clockInSuccessfully: "打卡成功",
    clockOutSuccessfully: "打卡下班成功",
    holidays: "假期",
    workingDay: "工作日",
    isInRange: "进入时钟输入或输出范围",
    isNotInRange: "不在时钟输入或时钟范围之外",
    deviceIdNotAvailable: "设备ID有问题，请联系IT管理员",
    // 统计页面相关文本
    lateArrival: "迟到",
    leavingEarly: "早退",
    notClockingIn: "缺卡",
    clockInTime: "签到时间：",
    clockOutTime: "签退时间：",
    noClockIn: "未签到",
    noClockOut: "未签退",
    noClockInRecord: "无打卡记录",
    noNeedToClock: "无需打卡",
    attendanceGroupNotConfigured: "考勤组未配置",
    pleaseClockInFirst: "请先打上班卡",
    handling: "处理中",
    clockedInUnderReview: "打卡审核中",
    // 补卡相关文案
    fixClockIn: "异常处理",
    fixClockInType: "补卡类型",
    reason: "原因",
    reasonPlaceholder: "请输入补卡原因",
    reasonRequired: "请填写原因",
    reasonMaxLength: "原因不能超过200个字符",
    confirm: "确认",
    cancel: "取消",
    fixClockInSuccess: "补卡提交成功",
    underReview: "审核中",
    reviewSuccess: "审核成功",
    reviewFailed: "审核失败",
    approvalPassed: "审批通过",
    normal: "正常",
    abnormalNoClockIn: "异常 - 未签到",
    abnormalLateClockIn: "异常 - 迟到签到",
    abnormalEarlyClockOut: "异常 - 早退签退",
    nextDay: "次日",
    attendanceRules: "考勤规则",
    fixedShifts: "固定班次",
    flexibleShifts: "弹性班次",
    startWorkTime: "上班时间 {time}",
    finishWorkTime: "下班时间 {time}",
    flexibleStart: "最早上班时间 {timeRange}",
    flexibleFinish: "最早下班时间 {time}",
    // 考勤规则弹窗文案
    attendanceRulesTitle: "考勤规则",
    attendanceRulesContent: `考勤类型：固定考勤

工作时间：09:00 - 18:00

休息时间：12:00 - 13:00

工作时长：8小时

打卡范围：需连接办公室WiFi

注意：打卡时请确保已连接到办公室WiFi网络。`,
    close: "关闭"
  },
  title: {
    attendance: "出席率",
    basicInfo: "基本信息",
    identityDetails: "身份详情",
    contactDetails: "联系方式",
    bankcardInfo: "添加银行卡",
    verifyIDPhoto: "验证身份证照片",
    faceVerification: "人脸验证",
    repaymentDetailsList: "还款详情",
    voiceCollection: "语音采集",
    completed: "完全的",
    reviewFailed: "审核失败"
  },
  common: {
    OKay: "好的",
    Close: "关闭",
    securityGuarantee: "安全保证",
    Help: "帮助",
    Edit: "编辑",
    Next: "下一个",
    footerNote: "注意：请填写您的真实联系信息，填写虚假信息将影响您的入职！"
  },
  calendar: {
    year: "年",
    month: "月",
    day: "天",
    Confirm: "确认",
    // v-calendar locale settings
    locale: {
      id: 'zh-CN',
      firstDayOfWeek: 1,
      masks: {
        title: 'YYYY年M月',
        weekdays: 'WW',
        navMonths: 'MMM',
        input: 'YYYY-MM-DD',
        dayPopover: 'YYYY年M月D日 dddd',
        data: ['L', 'YYYY-MM-DD', 'YYYY/MM/DD']
      }
    }
  },
  toastMsg: {
    isPending: "您填写的信息正在审核中",
    recordingLess: "录音时间小于5秒",
    noPermission: "请先授予必要的权限",
    loadResourceFailed: "系统正在升级，请稍后重试",
    networkError: "网络错误，请稍后重试",
    profileChanged: "您的个人资料已更改，请重新登录",
    permissionRequestFailed: "获取权限失败，请稍后再试"
  },
  login: {
    enterEmail: "请输入您的电子邮件地址",
    enterValidEmail: "请输入有效的电子邮件地址",
    inputNote: "帐户（您发给HR的个人电子邮件）",
    enterOTP: "请输入 4 位一次性密码",
    otpSentTo: "动态密码已发送至",
    getOtpAgain: "再次获取 OTP"
  },
  faceVerification: {
    scanningFaces: "扫描人脸",
    keep: "将脸保持在屏幕中间",
    note: "笔记",
    notice: "注意",
    photograph: "照片",
    noticeList: {
      0: "光线明亮",
      1: "手机没有震动",
      2: "没有其他人",
      3: "不要遮住脸"
    },
    noteTextList: {
      0: "1.请保持光线明亮",
      1: "2.请勿让他人进行人脸验证",
      2: "3.不要遮住脸或戴口罩",
      3: "4.请勿拍摄手机屏幕"
    }
  },
  voiceCollection: {
    noticeTextList: {
      0: '请点击开始按钮并朗读以下文字：',
      1: '注意：录音时长必须在5-10秒之间。'
    },
    guarantee: "我保证所提供的信息真实有效！",
    recordBtn: {
      start: "开始录音",
      end: "结束录音",
      reRecord: "重新录音"
    },
    invalidFileFormat: '文件格式无效',
  },
  personInfo: {
    authTitle: "授权提示",
    startAuth: "开始授权",
    welcome: "欢迎来到{appName}",
    obtainPermission: "我们会获取您的部分手机权限，以便您正常使用应用服务。",
    returnBtn: "返回",
    phoneNumber: "电话号码",
    male: "男性",
    female: "女性",
    email: "电子邮件",
    dataSafe: "您的数据 100% 安全",
    dataSafe2: "我们不会与任何人分享您的信息！",
    genderLower: "性别",
    genderUpper: "性别",
    emailLower: "员工的电子邮件 ID",
    emailUpper: "员工的电子邮件 ID",
    birthLower: "出生日期",
    birthUpper: "出生日期",
    educationalLower: "教育背景",
    educationalUpper: "教育背景",
    majorLower: "教育专业",
    majorUpper: "教育专业",
    addressLower: "员工的联系地址",
    addressUpper: "员工联系地址",
    maritalStatusLower: "婚姻状况",
    maritalStatusUpper: "婚姻状况",
    childrenNumLower: "儿童数量",
    childrenNumUpper: "儿童人数",
    lastCompanyNameLower: "姓氏公司名称",
    lastCompanyNameUpper: "姓氏公司名称",
    lastJobPositionLower: "最后的工作职位",
    lastJobPositionUpper: "最后的工作职位",
    single: "单身的",
    married: "已婚",
    seperated: "分离的",
    divorced: "离婚",
    widowed: "寡",
    educationLevel: {
      secondary: "高中学历",
      associate: "大专学历",
      bachelor: "本科学历",
      master: "硕士学历及以上",
      others: "其他"
    },
    schoolLower: "学校名称",
    schoolUpper: "学校名称"
  },
  identityInfo: {
    firstNameLower: "名",
    firstNameUpper: "名",
    middleNameLower: "中间名字",
    middleNameUpper: "中间名字",
    lastNameLower: "姓",
    lastNameUpper: "姓",
    idTypeLower: "身份类型",
    idTypeUpper: "身份证类型",
    idNumberLower: "身份证号码",
    idNumberUpper: "身份证号码",
    idCardFrontPhotoLower: "身份证正面",
    idCardFrontPhotoUpper: "身份证正面",
    idCardBackPhotoLower: "身份证背面",
    idCardBackPhotoUpper: "身份证背面（可选）",
    hasIdCardLower: '是否持有身份证',
    hasIdCardUpper: '是否持有身份证?(NIDA)',
    yes: '是',
    no: '否',
    pleaseUploadImage: '请上传图片文件',
    fileSizeCannotExceed: '文件大小不能超过 {size}MB',
    compressImageError: '图片压缩错误: {error}',
    formatTips: '建议使用 jpg、jpeg、png 格式的文件',
  },
  contactInfo: {
    emergencyContactRelationLower: "紧急联络关系",
    emergencyContactRelationUpper: "紧急联络关系",
    emergencyContactNameLower: "紧急联系人姓名",
    emergencyContactNameUpper: "紧急联系人姓名",
    emergencyContactPhoneLower: "紧急联系电话",
    emergencyContactPhoneUpper: "紧急联系电话",
    guarantorContactRelationLower: "担保人联系关系",
    guarantorContactRelationUpper: "担保人联系方式",
    guarantorContactNameLower: "担保人联系人姓名",
    guarantorContactNameUpper: "担保人联系人姓名",
    guarantorContactPhoneLower: "担保人联系电话",
    guarantorContactPhoneUpper: "担保人联系电话"
  },
  bankcardInfo: {
    bankNameLower: "银行名称",
    bankNameUpper: "银行名称",
    bankBranchLower: "银行分行",
    bankBranchUpper: "银行分行（可选）",
    acctNumberLower: "帐号",
    acctNumberUpper: "帐号"
  },
  result: {
    failedTitle: "审核失败提醒",
    failedContent: "失败原因",
    successTitle: "欢迎加入我们！",
    successContent: "您的信息已提交，请等待审核",
    rejectedSystem: "您目前不符合就业资格",
    modifyNow: "立即修改",
    gotoHome: "回家吧"
  },
  testMsg: {
    canNotEmpty: "不能为空",
    pleseInputCorrect: "请输入正确的信息",
    pleaseInputNumber: "请输入号码",
    wrongCell: "手机号码有误，请更正",
    notFill: "请先填写{field}",
    notSelect: "请先选择 {field}"
  }
};
