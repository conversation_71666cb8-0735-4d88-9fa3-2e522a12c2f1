module.exports = {
  home: {
    bannerText: "Construya un equipo eficiente a partir de {appName}",
    functionMenu: "Menú de funciones",
    attendance: "Asistencia",
    myProfile: "Mi perfil",
    todoItems: "Artículos pendientes",
    supplement: "Complementar información personal",
    supplementModify: "Hay un problema con su información de trabajo, por favor verifique",
    deadline: "Fecha límite",
    clockIn: "Regístrese",
    clockOut: "Reloj de salida",
    toComplete: "Para completar",
    rejectedSystem: "Actualmente no estas seleccionado para trabajar",
    logout: "Cerrar sesión",
    return: 'Volver'
  },
  logout: {
    confirmText: "¿Confirmar cierre de sesión?",
    confirm: "Confirmar",
    cancel: "Cancelar"
  },
  attendance: {
    saId: "ID del empleado",
    attendanceDate: "fecha de asistencia",
    startWork: "empezar a trabajar",
    offWork: "fuera del trabajo",
    clockIn: "Registro de entrada",
    clockOut: "Registro de salida",
    clockedIn: "Entrada Registrada",
    clockedOut: "Salida Registrada",
    updateClockIn: "Actualizar registro deentrada",
    updateClockOut: "Actualizar registro de salida",
    missingIn: "Falta en",
    clockInSuccessfully: "Registro de ingreso exitoso",
    clockOutSuccessfully: "Registro de salida exitosa",
    holidays: "Vacaciones",
    workingDay: "Día Laboral",
    isInRange: "Estas dentro del rango",
    isNotInRange: "Estas fuera del rango",
    deviceIdNotAvailable: "Hay un problema con la ID del dispositivo, comuníquese con el administrador de TI",
    // 新增的考勤相关文本
    attendanceRules: "Reglas de Asistencia",
    fixedShifts: "Turnos fijos",
    flexibleShifts: "Turnos flexibles",
    startWorkTime: "Comenzar trabajo {time}",
    finishWorkTime: "Terminar trabajo {time}",
    flexibleStart: "Inicio más temprano de trabajo {timeRange}",
    flexibleFinish: "Finalización más temprana de trabajo {time}",
    normal: "Normal",
    abnormalNoClockIn: "Anormal - Sin registro de entrada",
    abnormalLateClockIn: "Anormal - Registro de entrada tardío",
    abnormalEarlyClockOut: "Anormal - Salida temprana",
    nextDay: "Día siguiente",
    // 统计页面相关文本
    lateArrival: "Llegada tardía",
    leavingEarly: "Salida temprana",
    notClockingIn: "Sin registro de entrada",
    clockInTime: "Hora de entrada:",
    clockOutTime: "Hora de salida:",
    noClockIn: "Sin entrada",
    noClockOut: "Sin salida",
    noNeedToClock: "No necesita registrar",
    noClockInRecord: "Sin registro de entrada",
    attendanceGroupNotConfigured: "Grupo de asistencia no configurado",
    pleaseClockInFirst: "Por favor, registre la entrada primero",
    handling: "Procesando",
    clockedInUnderReview: "Entrada en revisión",
    // 补卡相关文案
    fixClockIn: "Manejo de Excepciones",
    fixClockInType: "Tipo de arreglo de registro",
    reason: "Razón",
    reasonPlaceholder: "Por favor ingrese la razón para arreglar el registro",
    reasonRequired: "Por favor complete la razón",
    reasonMaxLength: "La razón no puede exceder 200 caracteres",
    confirm: "Confirmar",
    cancel: "Cancelar",
    fixClockInSuccess: "Arreglo de registro enviado exitosamente",
    underReview: "En revisión",
    reviewSuccess: "Revisión exitosa",
    reviewFailed: "Revisión fallida",
    approvalPassed: "Aprobación exitosa",
    // 考勤规则弹窗文案
    attendanceRulesTitle: "Reglas de Asistencia",
    attendanceRulesContent: `Tipo de Asistencia: Asistencia Fija

Horas de Trabajo: 09:00 - 18:00

Tiempo de Descanso: 12:00 - 13:00

Duración del Trabajo: 8 horas

Rango de Registro: WiFi de oficina requerido

Nota: Asegúrese de estar conectado a la red WiFi de la oficina al registrar entrada o salida.`,
    close: "Cerrar",
  },
  title: {
    attendance: "Asistencia",
    statistics: "Estadísticas",
    basicInfo: "Información básica",
    identityDetails: "Detalles de identidad",
    contactDetails: "Detalles de contacto",
    bankcardInfo: "Agregar tarjeta bancaria",
    verifyIDPhoto: "Verificar foto de identificación",
    faceVerification: "Verificación facial",
    repaymentDetailsList: "Detalles de pago",
    voiceCollection: "Grabador de voz",
    completed: "Terminado",
    reviewFailed: "Revisión fallida"
  },
  common: {
    OKay: "Bien",
    Close: "Cerca",
    securityGuarantee: "garantia de seguridad",
    Help: "Ayuda",
    Edit: "Editar",
    Next: "Siguiente",
    footerNote: "Nota: Complete su información de contacto real, completar la información falsa afectará tu incorporación."
  },
  calendar: {
    year: "año",
    month: "mes",
    day: "día",
    Confirm: "Confirmar",
    // v-calendar locale settings
    locale: {
      id: 'es-ES',
      firstDayOfWeek: 1,
      masks: {
        title: 'MMMM YYYY',
        weekdays: 'WW',
        navMonths: 'MMM',
        input: 'YYYY-MM-DD',
        dayPopover: 'WWW, D [de] MMMM [de] YYYY',
        data: ['L', 'YYYY-MM-DD', 'DD/MM/YYYY']
      }
    }
  },
  toastMsg: {
    isPending: "La información que usted completó está bajo revisión.",
    recordingLess: "El tiempo de grabación es inferior a 5 segundos.",
    noPermission: "Primero conceda los permisos necesarios",
    loadResourceFailed: "El sistema se está actualizando, inténtalo de nuevo más tarde.",
    networkError: "Error de red, inténtelo de nuevo más tarde",
    profileChanged: "Su perfil ha sido actualizado, vuelva a iniciar sesión",
    pleaseEnableGpsPermission: "Por favor habilite el permiso de GPS",
    permissionRequestFailed: "Error al obtener permisos, inténtelo de nuevo más tarde"
  },
  login: {
    enterEmail: "Por favor ingrese su dirección de correo electrónico",
    enterValidEmail: "Por favor, introduce una dirección de correo electrónico válida",
    inputNote: "Cuenta (su correo electrónico personal para RR.HH.)",
    personalEmailTip: "Por favor, utilice la cuenta de correo electrónico personal registrada en el momento del empleo.",
    enterOTP: "Por favor ingrese OTP de 4 dígitos",
    otpSentTo: "otp enviado a",
    getOtpAgain: "Obtener OTP nuevamente"
  },
  faceVerification: {
    scanningFaces: "Escaneando rostros",
    keep: "Mantén tu cara en el medio de la pantalla.",
    note: "Nota",
    notice: "Aviso",
    photograph: "Fotografía",
    noticeList: {
      0: "Buena iluminación",
      1: "Mantener el telefono quieto",
      2: "Solo tu",
      3: "No tapes tu rostro"
    },
    noteTextList: {
      0: "1. Mantén buena iluminación.",
      1: "2. No permita que otros suplanten la verificación facial.",
      2: "3. No te cubras la cara ni uses mascarilla",
      3: "4. No tomes fotografías de pantallas de teléfonos móviles."
    }
  },
  voiceCollection: {
    noticeTextList: {
      0: 'Por favor, haga clic en el botón Iniciar y lea el siguiente texto:',
      1: 'Nota: Las grabaciones deben tener una duración de 5s-10s.'
    },
    guarantee: "Garantizo que la información proporcionada es verdadera y válida!",
    recordBtn: {
      start: "Iniciar",
      end: "Finalizar",
      reRecord: "Volver a grabar"
    },
    invalidFileFormat: 'Formato de archivo no válido',
  },
  personInfo: {
    authTitle: "Solicitud de autorización",
    startAuth: "Iniciar autorización",
    welcome: "Bienvenido a {appName}",
    obtainPermission: "Obtendremos algunos de los permisos de su teléfono para que pueda utilizar los servicios de la aplicación con normalidad.",
    returnBtn: "Devolver",
    phoneNumber: "Número de teléfono",
    male: "Masculino",
    female: "Femenino",
    email: "Correo electrónico",
    dataSafe: "Tus datos están 100% seguros",
    dataSafe2: "¡No compartimos tu información con nadie!",
    genderLower: "género",
    genderUpper: "Género",
    emailLower: "ID de correo electrónico del empleado",
    emailUpper: "ID de correo electrónico del empleado",
    birthLower: "fecha de nacimiento",
    birthUpper: "Fecha de nacimiento",
    educationalLower: "nivel academico",
    educationalUpper: "Nivel academico",
    majorLower: "especializaciones academicas adicionales",
    majorUpper: "Especializaciones academicas",
    addressLower: "direccióndel empleado",
    addressUpper: "Direccióndel empleado",
    maritalStatusLower: "Estado civil",
    maritalStatusUpper: "Estado civil",
    childrenNumLower: "numero de niños",
    childrenNumUpper: "Número de niños",
    lastCompanyNameLower: "apellido de la empresa",
    lastCompanyNameUpper: "Apellido Nombre de la Empresa",
    lastJobPositionLower: "último puesto de trabajo",
    lastJobPositionUpper: "Último puesto de trabajo",
    single: "Soltero",
    married: "Casado",
    seperated: "Separados",
    divorced: "Divorciado",
    widowed: "Viudo",
    educationLevel: {
      secondary: "Educación secundaria/Título de bachillerato",
      associate: "Grado de Asociado/Diploma",
      bachelor: "Licenciatura",
      master: "Máster o superior",
      others: "Otros"
    },
    schoolLower: "nombre de la escuela",
    schoolUpper: "Nombre de la Escuela"
  },
  identityInfo: {
    firstNameLower: "primer Nombre",
    firstNameUpper: "Primer Nombre",
    middleNameLower: "segundo nombre",
    middleNameUpper: "Segundo nombre",
    lastNameLower: "apellido",
    lastNameUpper: "Apellido",
    idTypeLower: "tipo de identificación",
    idTypeUpper: "Tipo de identificación",
    idNumberLower: "número de identificación",
    idNumberUpper: "Número de identificación",
    idCardFrontPhotoLower: "cara frontal del DNI/Tarjeta de identidicación",
    idCardFrontPhotoUpper: "Cara frontal del DNI/Tarjeta de identidicación",
    idCardBackPhotoLower: "cara reversa del DNI/Tarjeta de identidicación",
    idCardBackPhotoUpper: "Cara reversa del DNI/Tarjeta de identidicación(opcional))",
    hasIdCardLower: 'tiene documento de identidad',
    hasIdCardUpper: 'Tiene documento de identidad?(NIDA)',
    yes: 'Sí',
    no: 'No',
    pleaseUploadImage: 'por favor sube un archivo de imagen',
    fileSizeCannotExceed: 'el tamaño del archivo no puede exceder {size}MB',
    compressImageError: 'error de compresión de imagen: {error}',
    formatTips: 'Se recomienda utilizar archivos jpg, jpeg o png.',
  },
  contactInfo: {
    emergencyContactRelationLower: "relación de contacto de emergencia",
    emergencyContactRelationUpper: "Relación de contacto de emergencia",
    emergencyContactNameLower: "nombre del contacto de emergencia",
    emergencyContactNameUpper: "Nombre del contacto de emergencia",
    emergencyContactPhoneLower: "número de teléfono de contacto de emergencia",
    emergencyContactPhoneUpper: "Número de teléfono de contacto de emergencia",
    guarantorContactRelationLower: "relación de contacto garante",
    guarantorContactRelationUpper: "Relación de contacto del garante",
    guarantorContactNameLower: "nombre de contacto del garante",
    guarantorContactNameUpper: "Nombre de contacto del garante",
    guarantorContactPhoneLower: "número de teléfono de contacto del garante",
    guarantorContactPhoneUpper: "Número de teléfono de contacto del garante"
  },
  bankcardInfo: {
    bankNameLower: "nombre del banco",
    bankNameUpper: "Nombre del banco",
    bankBranchLower: "sucursal bancaria",
    bankBranchUpper: "Sucursal bancaria (opcional)",
    acctNumberLower: "número de cuenta",
    acctNumberUpper: "Número de cuenta"
  },
  result: {
    failedTitle: "Recordatorio por revisión fallida",
    failedContent: "Motivo del fracaso",
    successTitle: "Bienvenido a nuestra empresa!",
    successContent: "Su información ha sido enviada, espere la revisión.",
    rejectedSystem: "No fuiste seleccionado para trabajar con nosotros",
    modifyNow: "Modificar ahora",
    gotoHome: "ir a casa"
  },
  testMsg: {
    canNotEmpty: "no puede estar vacio",
    pleseInputCorrect: "Por favor ingrese la información correcta",
    pleaseInputNumber: "Por favor ingrese el número",
    wrongCell: "Número de celular incorrecto, por favor modificar",
    notFill: "Por favor complete el campo primero",
    notSelect: "Seleccione el campo primero"
  }
};
