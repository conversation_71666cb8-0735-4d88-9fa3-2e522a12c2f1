module.exports = {
  home: {
    bannerText: "Construisez une équipe efficace à partir de {appName}",
    functionMenu: "Menu Fonction",
    attendance: "Présence",
    myProfile: "Mon profil",
    todoItems: "Éléments à faire",
    supplement: "Compléter les informations personnelles",
    supplementModify: "Il y a un problème avec vos informations d'emploi, veuillez vérifier",
    deadline: "Date limite",
    clockIn: "Pointer à l'arrivée",
    clockOut: "Pointage de départ",
    toComplete: "Pour compléter",
    rejectedSystem: "Vous n'êtes actuellement pas admissible à un emploi",
    logout: "Déconnexion",
    return: 'Retour'
  },
  logout: {
    confirmText: "Confirmer la déconnexion ?",
    confirm: "Confirmer",
    cancel: "Annuler"
  },
  attendance: {
    saId: "ID de l'employé",
    attendanceDate: "Date de présence",
    startWork: "Commencer le travail",
    offWork: "Hors travail",
    clockIn: "Pointer à l'arrivée",
    clockOut: "Pointage de depart",
    clockedIn: "Pointé",
    clockedOut: "Pointé",
    updateClockIn: "Mettre à jour l'heure d'arrivée",
    updateClockOut: "Mettre à jour le pointage de depart",
    missingIn: "Manquant dans",
    clockInSuccessfully: "Pointage réussi",
    clockOutSuccessfully: "pointage de Départ réussi",
    holidays: "Vacances",
    workingDay: "Jour de Travail",
    isInRange: "Vous avez entré l'horloge dans la plage",
    isNotInRange: " hors plage",
    deviceIdNotAvailable: "Il y a un problème avec l'ID de l'appareil, veuillez contacter l'administrateur informatique",
    // 新增的考勤相关文本
    attendanceRules: "Règles de Présence",
    fixedShifts: "Équipes fixes",
    flexibleShifts: "Équipes flexibles",
    startWorkTime: "Commencer le travail {time}",
    finishWorkTime: "Terminer le travail {time}",
    flexibleStart: "Début de travail le plus tôt {timeRange}",
    flexibleFinish: "Fin de travail le plus tôt {time}",
    normal: "Normale",
    abnormalNoClockIn: "Anormal - Pas de pointage d'arrivée",
    abnormalLateClockIn: "Anormal - Pointage d'arrivée tardif",
    abnormalEarlyClockOut: "Anormal - Départ anticipé",
    nextDay: "Jour suivant",
    // 统计页面相关文本
    lateArrival: "Arrivée tardive",
    leavingEarly: "Départ anticipé",
    notClockingIn: "Pas de pointage d'arrivée",
    clockInTime: "Heure d'arrivée:",
    clockOutTime: "Heure de départ:",
    noClockIn: "Pas d'arrivée",
    noClockOut: "Pas de départ",
    noNeedToClock: "Pas besoin de pointer",
    noClockInRecord: "Aucun enregistrement de pointage",
    attendanceGroupNotConfigured: "Groupe de présence non configuré",
    pleaseClockInFirst: "Veuillez d'abord pointer à l'arrivée",
    handling: "En traitement",
    clockedInUnderReview: "Pointage en cours d'examen",
    // 补卡相关文案
    fixClockIn: "Gestion des Exceptions",
    fixClockInType: "Type de correction de pointage",
    reason: "Raison",
    reasonPlaceholder: "Veuillez entrer la raison de la correction du pointage",
    reasonRequired: "Veuillez remplir la raison",
    reasonMaxLength: "La raison ne peut pas dépasser 200 caractères",
    confirm: "Confirmer",
    cancel: "Annuler",
    fixClockInSuccess: "Correction de pointage soumise avec succès",
    underReview: "En cours d'examen",
    reviewSuccess: "Examen réussi",
    reviewFailed: "Examen échoué",
    approvalPassed: "Approbation réussie",
    // 考勤规则弹窗文案
    attendanceRulesTitle: "Règles de Présence",
    attendanceRulesContent: `Type de Présence: Présence Fixe

Heures de Travail: 09:00 - 18:00

Temps de Pause: 12:00 - 13:00

Durée de Travail: 8 heures

Plage de Pointage: WiFi de bureau requis

Note: Veuillez vous assurer d'être connecté au réseau WiFi du bureau lors du pointage d'entrée ou de sortie.`,
    close: "Fermer",
  },
  title: {
    attendance: "Présence",
    statistics: "Statistiques",
    basicInfo: "Informations de base",
    identityDetails: "Détails d'identité",
    contactDetails: "Coordonnées",
    bankcardInfo: "Ajouter une carte bancaire",
    verifyIDPhoto: "Vérifier la photo d'identité",
    faceVerification: "Vérification du visage",
    repaymentDetailsList: "Détails du remboursement",
    voiceCollection: "Collection de voix",
    completed: "Complété",
    reviewFailed: "Échec de la révision"
  },
  common: {
    OKay: "D'accord",
    Close: "Fermer",
    securityGuarantee: "garantie de sécurité",
    Help: "Aide",
    Edit: "Modifier",
    Next: "Suivant",
    footerNote: "Remarque : Veuillez remplir vos véritables coordonnées, remplir les fausses informations affectera votre intégration !"
  },
  calendar: {
    year: "année",
    month: "mois",
    day: "jour",
    Confirm: "Confirmer",
    // v-calendar locale settings
    locale: {
      id: 'fr-FR',
      firstDayOfWeek: 1,
      masks: {
        title: 'MMMM YYYY',
        weekdays: 'WW',
        navMonths: 'MMM',
        input: 'YYYY-MM-DD',
        dayPopover: 'dddd D MMMM YYYY',
        data: ['L', 'YYYY-MM-DD', 'DD/MM/YYYY']
      }
    }
  },
  toastMsg: {
    isPending: "Les informations que vous avez renseignées sont en cours de révision",
    recordingLess: "La durée d'enregistrement est inférieure à 5 secondes",
    noPermission: "Veuillez d'abord accorder les autorisations nécessaires",
    loadResourceFailed: "Le système est en cours de mise à niveau, veuillez réessayer plus tard",
    networkError: "Erreur réseau, veuillez réessayer plus tard",
    profileChanged: "Votre profil a été modifié, veuillez vous reconnecter",
    pleaseEnableGpsPermission: "Veuillez activer l'autorisation GPS",
    permissionRequestFailed: "Échec de l'obtention des autorisations, veuillez réessayer plus tard"
  },
  login: {
    enterEmail: "Veuillez entrer votre adresse e-mail",
    enterValidEmail: "S'il vous plaît, mettez une adresse email valide",
    inputNote: "Compte (Votre email personnel aux RH)",
    personalEmailTip: "Veuillez utiliser le compte de messagerie personnel enregistré au moment de l'embauche.",
    enterOTP: "Veuillez saisir OTP à 4 chiffres",
    otpSentTo: "otp envoyé à",
    getOtpAgain: "Obtenez à nouveau OTP"
  },
  faceVerification: {
    scanningFaces: "Scanner des visages",
    keep: "Gardez votre visage au milieu de l'écran",
    note: "Note",
    notice: "Avis",
    photograph: "Photographier",
    noticeList: {
      0: "Lumière brillante",
      1: "Pas de vibration du téléphone",
      2: "Aucune autre personne",
      3: "Ne couvrez pas les visages"
    },
    noteTextList: {
      0: "1. Veuillez garder la lumière vive",
      1: "2. S'il vous plaît, ne laissez pas les autres effectuer une vérification faciale",
      2: "3. Ne vous couvrez pas le visage et ne portez pas de masque",
      3: "4. Veuillez ne pas prendre de photos des écrans de téléphones portables"
    }
  },
  voiceCollection: {
    noticeTextList: {
      0: 'Veuillez cliquer sur le bouton Démarrer et lire le texte suivant :',
      1: 'Note : Les enregistrements doivent durer entre 5 et 10 secondes.'
    },
    guarantee: "Je garantis que les informations fournies sont vraies et valides!",
    recordBtn: {
      start: "Commencer",
      end: "Terminer",
      reRecord: "Réenregistrer"
    },
    invalidFileFormat: 'Format de fichier invalide',
  },
  personInfo: {
    authTitle: "Invite d'autorisation",
    startAuth: "Autorisation de démarrage",
    welcome: "Bienvenue sur {appName}",
    obtainPermission: "Nous obtiendrons certaines autorisations de votre téléphone afin que vous puissiez utiliser les services de l'application normalement.",
    returnBtn: "Retour",
    phoneNumber: "Numéro de téléphone",
    male: "Mâle",
    female: "Femelle",
    email: "E-mail",
    dataSafe: "Vos données sont 100% sécurisées",
    dataSafe2: "Nous ne partageons vos informations avec personne !",
    genderLower: "genre",
    genderUpper: "Genre",
    emailLower: "identifiant de messagerie de l'employé",
    emailUpper: "Identifiant de messagerie de l'employé",
    birthLower: "date de naissance",
    birthUpper: "Date de naissance",
    educationalLower: "contexte éducatif",
    educationalUpper: "Contexte éducatif",
    majorLower: "majeure en éducation",
    majorUpper: "Majeure en éducation",
    addressLower: "l'adresse de contact de l'employé",
    addressUpper: "Adresse de contact de l'employé",
    maritalStatusLower: "état civil",
    maritalStatusUpper: "État civil",
    childrenNumLower: "nombre d'enfants",
    childrenNumUpper: "Nombre d'enfants",
    lastCompanyNameLower: "nom de l'entreprise",
    lastCompanyNameUpper: "Nom de l'entreprise",
    lastJobPositionLower: "dernier poste",
    lastJobPositionUpper: "Dernier poste",
    single: "Célibataire",
    married: "Marié",
    seperated: "Séparé",
    divorced: "Divorcé",
    widowed: "Veuf",
    educationLevel: {
      secondary: "Enseignement secondaire/Diplôme de fin d'études",
      associate: "Diplôme d'associé",
      bachelor: "Licence",
      master: "Master ou diplôme supérieur",
      others: "Autres"
    },
    schoolLower: "nom de l'école",
    schoolUpper: "Nom de l'école"
  },
  identityInfo: {
    firstNameLower: "prénom",
    firstNameUpper: "Prénom",
    middleNameLower: "deuxième prénom",
    middleNameUpper: "Deuxième prénom",
    lastNameLower: "nom de famille",
    lastNameUpper: "Nom de famille",
    idTypeLower: "type d'identifiant",
    idTypeUpper: "Type d'identification",
    idNumberLower: "numéro d'identification",
    idNumberUpper: "Numéro d'identification",
    idCardFrontPhotoLower: "recto de la carte d'identité",
    idCardFrontPhotoUpper: "Face avant de la carte d'identité",
    idCardBackPhotoLower: "verso de la carte d'identité",
    idCardBackPhotoUpper: "Verso de la carte d'identité (facultatif)",
    hasIdCardLower: 'avoir une carte d\'identité',
    hasIdCardUpper: 'Avoir une carte d\'identité?(NIDA)',
    yes: 'Oui',
    no: 'Non',
    pleaseUploadImage: 'veuillez télécharger un fichier image',
    fileSizeCannotExceed: 'la taille du fichier ne peut pas dépasser {size}MB',
    compressImageError: 'erreur de compression d\'image: {error}',
    formatTips: 'Il est recommandé d\'utiliser des fichiers jpg, jpeg ou png',
  },
  contactInfo: {
    emergencyContactRelationLower: "relation à contacter en cas d'urgence",
    emergencyContactRelationUpper: "Relation de contact d'urgence",
    emergencyContactNameLower: "nom de la personne à contacter en cas d'urgence",
    emergencyContactNameUpper: "Nom du contact d'urgence",
    emergencyContactPhoneLower: "numéro de téléphone du contact d'urgence",
    emergencyContactPhoneUpper: "Numéro de téléphone du contact d'urgence",
    guarantorContactRelationLower: "relation de contact garant",
    guarantorContactRelationUpper: "Relation Contact Garant",
    guarantorContactNameLower: "nom du contact du garant",
    guarantorContactNameUpper: "Nom du contact du garant",
    guarantorContactPhoneLower: "numéro de téléphone du garant",
    guarantorContactPhoneUpper: "Numéro de téléphone du garant"
  },
  bankcardInfo: {
    bankNameLower: "nom de la banque",
    bankNameUpper: "Nom de la banque",
    bankBranchLower: "agence bancaire",
    bankBranchUpper: "Agence bancaire (facultatif)",
    acctNumberLower: "numéro de compte",
    acctNumberUpper: "Numéro de compte"
  },
  result: {
    failedTitle: "Rappel en cas d'échec d'un examen",
    failedContent: "Raison de l'échec",
    successTitle: "Bienvenue à nous rejoindre !",
    successContent: "Vos informations ont été soumises, veuillez attendre l'examen",
    rejectedSystem: "Vous n'êtes actuellement pas admissible à un emploi",
    modifyNow: "Modifier maintenant",
    gotoHome: "Rentre à la maison"
  },
  testMsg: {
    canNotEmpty: "Ne peut pas être vide",
    pleseInputCorrect: "Veuillez saisir des informations correctes",
    pleaseInputNumber: "Veuillez saisir le numéro",
    wrongCell: "Mauvais numéro de téléphone portable, veuillez le corriger",
    notFill: "Veuillez d'abord remplir {field}",
    notSelect: "Veuillez d'abord sélectionner {field}"
  }
};
