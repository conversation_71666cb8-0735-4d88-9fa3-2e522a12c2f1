import Vue from 'vue';
import VueI18n from 'vue-i18n';
import { Locale } from 'vant'
import enUS from 'vant/es/locale/lang/en-US';
import esES from 'vant/es/locale/lang/es-ES';
import frFR from 'vant/es/locale/lang/fr-FR';
// vant 不支持斯瓦西里语，契维语 这里按照官方文档 vant/es/locale/lang/en-US'，本地写一个斯瓦西里语文件
import swSw from '@/i18n/vant/sw';
import twTw from "@/i18n/vant/twTw";
// 本地文件
import enLocale from '@/i18n/locale/en' // 英文语言包
import swLocale from '@/i18n/locale/sw' // 斯瓦希里语
import frLocale from '@/i18n/locale/fr' // 法语。语言包
import esLocale from '@/i18n/locale/es' // 西班牙语
import twLocale from '@/i18n/locale/tw' // 契维语

Vue.use(VueI18n);

// localStorage获取当前语言类型(初次本地不存在'lang'字段存储，默认设置为'zh_CN')
const lang = localStorage.getItem('lang') || 'en';
console.log('初始语言类型', lang);

// 语言包的类型合并
const messages = {
  en: {
    ...enUS,
    ...enLocale
  },
  sw: {
    ...swSw,
    ...swLocale
  },
  fr: {
    ...frFR,
    ...frLocale
  },
  es: {
    ...esES,
    ...esLocale
  },
  tw: {
    ...twTw,
    ...twLocale
  }
};

// export default new VueI18n({
//   locale: lang, // set locale
//   messages: messages, // set locale messages
//   silentTranslationWarn: true
// });
const i18n = new VueI18n({
  locale: lang, // set locale
  messages: messages // set locale messages
})


// 更新vant组件库本身的语言变化，支持国际化
function vantLocales(lang) {
  if (lang === 'en') {
    Locale.use(lang, enUS)
  } else if (lang === 'fr') {
    Locale.use(lang, frFR)
  } else if (lang === 'es') {
    Locale.use(lang, esES)
  } else if (lang === 'sw') {
    Locale.use(lang, swSw)
  }
}

export const LANG_MAP = {
  en: 'en-US',
  fr: 'fr-FR',
  es: 'es-ES',
  sw: 'sw',
  tw: 'tw'
}

// 用于传给后端accept-language的语言类型转换
function transformLang(lang) {
  return LANG_MAP[lang] || lang;
}

export { i18n, vantLocales, transformLang }
