/*
* 状态
INVITING("inviting", "邀约中")
* 审核中
SCREENING("screening", "机审中")
PENDING("pending", "等待审批")
CHEKING("checking", "审批中")
* 通过
PASSED_SYSTEM("passed_system", "系统审批通过")
PASSED_MANUAL("passed_manual", "人工审批通过")
* 拒绝
REJECTED_SYSTEM("rejected_system", "系统审批拒绝"),
REJECTED_MANUAL("rejected_manual", "人工审批拒绝"),
CANCELED("cancelled", "取消邀约")
RESIGN("resign", "离职")
* */
import { isIOS } from "@/utils/tools";

export const INVITING_STATUS_ENUMS = {
  INVITING: "inviting",
  SCREENING: "screening",
  PENDING: "pending",
  CHEKING: "checking",
  PASSED_SYSTEM: "passed_system",
  PASSED_MANUAL: "passed_manual",
  REJECTED_SYSTEM: "rejected_system",
  REJECTED_MANUAL: "rejected_manual",
  CANCELED: "cancelled",
  RESIGN: "resign"
}

export const EMAIL_REGEX = /^([a-zA-Z0-9]+[_|.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/

/*
* 业务相关权限：
获取WiFi mac地址、经纬度：
android.permission.ACCESS_FINE_LOCATION
录音：
android.permission.RECORD_AUDIO
拍照：
android.permission.CAMERA

数据采集相关权限：
短信：
android.permission.READ_SMS
通话记录：
android.permission.READ_CALL_LOG
通讯录：
android.permission.READ_CONTACTS
传感器：
android.permission.HIGH_SAMPLING_RATE_SENSORS
设备信息：
android.permission.READ_PHONE_STATE
*
* */

export const PERMISSION_ENUMS = {
  ACCESS_COARSE_LOCATION: "android.permission.ACCESS_FINE_LOCATION",
  RECORD_AUDIO: "android.permission.RECORD_AUDIO",
  CAMERA: "android.permission.CAMERA",
  READ_SMS: "android.permission.READ_SMS",
  READ_CONTACTS: "android.permission.READ_CONTACTS",
}

// ios 系统不支持通讯录权限
if (isIOS()) {
  delete PERMISSION_ENUMS.READ_CONTACTS
}
