import Vue from "vue";
import Router from "vue-router";
import Home from '@/views/home/<USER>'
import store from '@/store'

Vue.use(Router);
const router = new Router({
  // 带有keepAlive缓存组件记住滚动条位置
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      if (from.meta.keepAlive) {
        from.meta.savedPosition = document.body.scrollTop;
      }
      return { x: 0, y: to.meta.savedPosition || 0 };
    }
  },
  routes: [
    // / redirect to /home
    {
      path: "/",
      redirect: "/home"
    },
    // login-input
    {
      path: "/login-input",
      name: "login-input",
      meta: {
        keepAlive: true
      },
      component: () =>
        import(/* webpackChunkName: "login-input" */ "@/views/login/login-input.vue")
    },
    // login-otp
    {
      path: "/login-otp",
      name: "login-otp",
      meta: {
        title: ""
      },
      component: () =>
        import(/* webpackChunkName: "login-otp" */ "@/views/login/login-otp.vue")
    },
    {
      path: "/home",
      name: "home",
      meta: {
        title: ""
      },
      component: Home
    },
    {
      path: "/attendance",
      name: "attendance",
      meta: {
        title: ""
      },
      component: () => import(/* webpackChunkName: "attendance" */ "@/views/attendance/index.vue")
    },
    { // 原生能力demo
      path: "/nativeDemo",
      name: "nativeDemo",
      meta: {
        title: ""
      },
      component: () =>
        import(/* webpackChunkName: "nativeDemo" */ "@/views/nativeDemo/nativeDemo.vue")
    },
    /**
     * 授信前的流程
     * 个人资料，证件页，活体
     */
    {
      path: "/personInfo",
      name: "personInfo",
      meta: {
        title: ""
      },
      component: () =>
        import(/* webpackChunkName: "personInfo" */ "@/views/entryProcess/personInfo")
    },
    {
      path: "/identityInfo",
      name: "identityInfo",
      meta: {
        title: ""
      },
      component: () =>
        import(/* webpackChunkName: "identityInfo" */ "@/views/entryProcess/identityInfo/identityInfo.vue")
    },
    {
      path: "/contactInfo",
      name: "contactInfo",
      meta: {
        title: ""
      },
      component: () =>
        import(/* webpackChunkName: "contactInfo" */ "@/views/entryProcess/contactInfo/contactInfo.vue")
    },
    {
      path: "/bankCardInfo",
      name: "bankCardInfo",
      meta: {
        title: ""
      },
      component: () =>
        import(/* webpackChunkName: "bankCardInfo" */ "@/views/entryProcess/bankCardInfo/bankCardInfo.vue")
    },
    {
      path: "/faceVerification",
      name: "faceVerification",
      meta: {
        title: ""
      },
      component: () => import(/* webpackChunkName: "faceVerification" */ "@/views/entryProcess/faceVerification/faceVerification.vue")
    },
    {
      path: "/voiceCollection",
      name: "voiceCollection",
      meta: {
        title: ""
      },
      component: () => import(/* webpackChunkName: "voiceCollection" */ "@/views/entryProcess/voiceCollection/voiceCollection.vue")
    },
    // result
    {
      path: "/result",
      name: "result",
      meta: {
        title: ""
      },
      component: () =>
        import(/* webpackChunkName: "result" */ "@/views/entryProcess/result/result.vue")
    },
    {
      path: "/unsupported",
      name: "unsupported",
      meta: {
        title: ""
      },
      component: () =>
        import(/* webpackChunkName: "unsupported" */ "@/views/unsupported/unsupported.vue")
    }
  ]
});

// 需要验证登录的路由
const authRoutes = [
  'attendance',
  'personInfo',
  'identityInfo',
  'contactInfo',
  'bankCardInfo',
  'faceVerification',
  'voiceCollection',
  'result'
]

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const isDebug = to.query.debug !== undefined;
  const isLoginPage = ['login-input', 'login-otp'].includes(to.name);

  // 如果 debug 参数存在，直接通过，忽略之后的验证
  if (isDebug) {
    next();
    return;
  }

  // 如果已登录且访问登录页面，重定向到 /home
  const hasToken = localStorage.getItem('token') || localStorage.getItem('tempToken');
  if (hasToken) {
    if (isLoginPage) {
      next('/home')
      return
    }
  } else {
    // 添加目标路由检查防止无限重定向
    if (!isLoginPage) {
      next('/login-input');
      return;
    }
  }


  // 如果是需要验证的路由且没有用户信息，重定向到 /home
  if (authRoutes.includes(to.name) && !store.state.userInfo?.id) {
    next('/home');
    return;
  }

  // 其他情况，正常通过
  next();
});

router.onReady(() => {
  console.log('第一个路由渲染完成时间:', new Date().toLocaleString());
});

export default router;
