<template>
  <div class="flex-col page">
    <public-header
      pageName="Attendance Rules"
      headerColor="#fff"
    />
    <div class="flex-col group_2 mt-39">
      <div class="flex-row">
        <img
          class="image_5"
          src=""
        />
        <div class="flex-col flex-1 items-start group_3 ml-25 mr-25">
          <span class="font_2 text_4">Hello, {{ userName }}</span>
          <span class="text_5 mt-19">{{ attendanceRules.attendanceName }} Attendance Group</span>
        </div>
      </div>
      <div class="flex-col relative view_2">
        <div class="flex-col section_1">
          <span class="self-start font_3 text_6">Attendance Shift:</span>
          <div class="flex-col self-stretch group_4 mt-22">
          <span class="self-start font_4 text_7">
            Fixed shifts
            <br />
            Monday to Friday: 9am-5pm, Saturday: 9pm-5:30pm
          </span>
            <span class="self-stretch font_4 mt-3">
            Late arrival for more than 0.5 hours will be recorded as missing card or early departure for more than 0.5
            hours will be recorded as missing card the latest time to punch off is 1:00 am the next day
          </span>
          </div>
        </div>
        <div class="flex-col relative section_6">
          <span class="self-start font_3 text_14">Attendance Rules:</span>
          <div class="flex-col items-start self-stretch group_5 mt-22">
            <span class="font_4 text_16">No card replacement is allowed after missing card; Punching card in PC/Laptop is not allowed; if you are on the list of employees working from home,you can punch in outside.</span>
          </div>
        </div>
        <div class="group_9 pos_2"></div>
        <div class="flex-col relative section_3">
          <span class="self-start font_3 text_12">Attendance range:</span>
          <div class="self-stretch divider view"></div>
          <span class="self-start text_13">clock-in location</span>
          <div class="flex-col self-stretch group_list">
            <div class="flex-row flex-wrap">
              <div class="flex-col justify-start items-center text-wrapper">
                <span class="font_5">Transsion Bulding</span>
              </div>
              <div class="flex-col justify-start items-center text-wrapper">
                <span class="font_5">Transsion Bulding</span>
              </div>
              <div class="flex-col justify-start items-center text-wrapper">
                <span class="font_5">Transsion Bulding</span>
              </div>
              <div class="flex-col justify-start items-center text-wrapper">
                <span class="font_5">Transsion Bulding</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-col relative section_6">
          <span class="self-start font_3 text_14">Attendance Administrator:</span>
          <div class="self-stretch divider view_3"></div>
          <div class="flex-col self-start group_7">
            <span class="self-stretch font_4">
              If you have any questions about the attendance rules, please consult your attendance group administrator
            </span>
            <div class="flex-col self-stretch group_list">
              <div class="flex-row flex-wrap">
                <div class="flex-col justify-start items-center text-wrapper">
                  <span class="font_5">Juliet Anna</span>
                </div>
                <div class="flex-col justify-start items-center text-wrapper">
                  <span class="font_5">Juliet Anna</span>
                </div>
                <div class="flex-col justify-start items-center text-wrapper">
                  <span class="font_5">Juliet Anna</span>
                </div>
                <div class="flex-col justify-start items-center text-wrapper">
                  <span class="font_5">Juliet Anna</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  components: {},
  props: {},
  data() {
    return {
    };
  },
  computed: {
    ...mapState(['uid', 'userInfo', 'attendanceRules']),
    userName() {
      const { firstName, middleName, lastName } = this.userInfo || {};
      if (!middleName) {
        return `${firstName} ${lastName}`;
      }
      return `${firstName} ${middleName} ${lastName}`;
    },
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.mt-39 {
  margin-top: 39px;
}
.ml-25 {
  margin-left: 25px;
}
.mr-25  {
  margin-right: 25px;
}
.mt-19 {
  margin-top: 19px;
}
.mt-3 {
  margin-top: 3px;
}
.ml-15 {
  margin-left: 15px;
}
.page {
  padding-bottom: 108px;
  background-color: #f1f3f6;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  .section_2 {
    padding: 45px 32px 47.5px;
    background-color: #ffffff;
    .image_4 {
      width: 44px;
      height: 44px;
    }
    .pos {
      position: absolute;
      left: 32px;
      top: 50%;
      transform: translateY(-50%);
    }
    .text_3 {
      line-height: 27.5px;
    }
  }
  .group_2 {
    margin-right: 24px;
    margin-left: 24px;
    .image_5 {
      width: 88px;
      height: 88px;
    }
    .group_3 {
      margin-top: 6.5px;
      .text_4 {
        font-weight: 700;
        line-height: 38px;
      }
      .text_5 {
        color: #7b8da8;
        font-size: 28px;
        line-height: 32px;
      }
    }
    .view_2 {
      margin-top: 24px;
      .section_1 {
        padding: 28px 24px 0;
        background-color: #ffffff;
        border-radius: 16px;
        box-shadow: 0px 6px 20px #0000001a;
        .text_6 {
          line-height: 20px;
        }
        .group_4 {
          padding: 18px 0 29.5px;
          border-top: solid 2px #dbe1ea;
          .text_7 {
            color: #0074ff;
            word-break: break-word;
          }
        }
      }
      .section_6 {
        margin-top: 24px;
        padding: 28.5px 24px 0;
        background-color: #ffffff;
        border-radius: 16px;
        box-shadow: 0px 6px 20px #0000001a;
        .text_14 {
          line-height: 20px;
        }
        .group_5 {
          padding: 18px 0 27px;
          border-top: solid 2px #dbe1ea;
          .text_16 {
            margin-left: 2px;
          }
          .text_9 {
            margin-left: 2px;
            margin-top: -9px;
          }
          .text_10 {
            margin-top: -9px;
          }
          .text_11 {
            margin-top: -9px;
          }
        }
        .view_3 {
          margin-top: 24px;
        }
        .group_7 {
          padding: 16px 0 27px;
        }
      }
      .font_3 {
        font-size: 26px;
        line-height: 19px;
        color: #1b3155;
      }
      .font_4 {
        font-size: 24px;
        line-height: 52px;
        color: #7b8da8;
      }
      .group_9 {
        height: 300px;
      }
      .pos_2 {
        position: absolute;
        left: 0;
        right: 0;
        top: 424px;
      }
      .section_3 {
        margin-top: 24px;
        padding: 28.5px 24px 33px;
        background-color: #ffffff;
        border-radius: 16px;
        box-shadow: 0px 6px 20px #0000001a;
        .text_12 {
          line-height: 25px;
        }
        .view {
          margin-top: 18.5px;
        }
        .text_13 {
          margin-top: 30px;
          color: #0074ff;
          font-size: 24px;
          line-height: 18px;
        }
      }
      .group_list {
        margin-top: 25px;
        .text-wrapper {
          margin-right: 15px;
          margin-bottom: 15px;
          padding: 14px 22px;
          border-radius: 8px;
          border: solid 1px #0074ff;
        }
      }
      .divider {
        background-color: #dbe1ea;
        height: 2px;
      }
      .font_5 {
        font-size: 20px;
        line-height: 19px;
        color: #0074ff;
      }
    }
  }
  .font_2 {
    font-size: 36px;
    color: #1b3155;
  }
}
</style>
