<template>
  <div class="flex-col page">
    <public-header
      :pageName="$t('title.statistics')"
      headerColor="#fff"
    />
    <div class="flex-col mb-26 flex-1">
      <div class="flex-col justify-start group_2">
        <div class="flex-col section_3">
          <div class="flex-col group_3">
            <span class="self-start text_4">{{ currentDateFormatted }}</span>
            <div class="flex-row items-start equal-division section_4 mt-25">
              <div class="flex-col flex-1 equal-division-item group_5">
                <span class="self-center font_2">{{ attendanceStats.lateArrivalNum }}</span>
                <span class="font_3 mt-22">{{ $t('attendance.lateArrival') }}</span>
              </div>
              <div class="flex-col flex-1 items-center equal-division-item_2 group_5">
                <span class="self-center font_2">{{ attendanceStats.leavingEarlyNum }}</span>
                <span class="font_3 mt-22">{{ $t('attendance.leavingEarly') }}</span>
              </div>
              <div class="flex-col flex-1 equal-division-item_3 group_5">
                <span class="self-center font_2">{{ attendanceStats.notClockingInNum }}</span>
                <span class="font_3 mt-22">{{ $t('attendance.notClockingIn') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <calendar :attendance-data="calendarAttendanceData" @date-selected="onDateSelected" />

      <!-- 考勤详情显示区域 -->
      <div v-if="selectedDateData" class="flex-col">
        <!-- 上班打卡详情区块 -->
        <div class="flex-col section_8">
          <div class="flex-row justify-between items-center mb-20">
            <span class="text_detail_title">{{ $t('attendance.clockIn') }}</span>
            <span class="text_39" :class="getOnStatusClass(selectedDateData)">{{ getOnStatusText(selectedDateData) }}</span>
          </div>
          <div class="flex-row justify-between items-center">
            <div class="flex-col">
              <span class="font_7" :class="getClockInTimeClass(selectedDateData)">{{ getClockInDisplayText(selectedDateData) }}</span>
            </div>
            <!-- 上班打卡审批按钮 -->
            <div v-if="shouldShowApprovalButton(selectedDateData.onStatus)" class="flex-col justify-start items-center text-wrapper_5 fix-clock-in-button" :class="getApprovalStatusClass(selectedDateData.onApprovalStatus)" @click="openOnApprovalDialog(selectedDateData.onApprovalStatus)">
              <span class="font_3 text_41">{{ getApprovalBtnText(selectedDateData.onApprovalStatus) }}</span>
            </div>
          </div>
        </div>

        <!-- 下班打卡详情区块 -->
        <div class="flex-col section_8">
          <div class="flex-row justify-between items-center mb-20">
            <span class="text_detail_title">{{ $t('attendance.clockOut') }}</span>
            <span class="text_39" :class="getOffStatusClass(selectedDateData)">{{ getOffStatusText(selectedDateData) }}</span>
          </div>
          <div class="flex-row justify-between items-center">
            <div class="flex-col">
              <span class="font_7" :class="getClockOutTimeClass(selectedDateData)">{{ getClockOutDisplayText(selectedDateData) }}</span>
            </div>
            <!-- 下班打卡审批按钮 -->
            <div v-if="shouldShowApprovalButton(selectedDateData.offStatus)" class="flex-col justify-start items-center text-wrapper_5 fix-clock-in-button"  :class="getApprovalStatusClass(selectedDateData.offApprovalStatus)" @click="openOffApprovalDialog(selectedDateData.offApprovalStatus)">
              <span class="font_3 text_41">{{ getApprovalBtnText(selectedDateData.offApprovalStatus) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 无考勤记录时的显示 -->
      <div v-else class="flex-col section_8">
        <div class="flex-row justify-center items-center">
          <span class="text_no_record">{{ $t('attendance.noClockInRecord') }}</span>
        </div>
      </div>
    </div>

    <!-- 补卡弹窗 -->
    <fix-clock-in-dialog
      v-model="showFixClockInDialog"
      :sign-type="currentSignType"
      :attendance-date="formatSelectedDate"
      @submit="handleFixClockInSubmit"
    />
  </div>
</template>

<script>
import Calendar from "./calendar.vue";
import FixClockInDialog from "@/components/fixClockInDialog.vue";
import { getAttendanceClockRecord, submitAttendanceApproval } from "@/axios/api";
import dayjs, { toLocalTime } from '@/utils/dayjs';
export default {
  components: {
    Calendar,
    FixClockInDialog
  },
  props: {},
  data() {
    return {
      currentDateFormatted: dayjs().format('YYYY-MM-DD'),
      selectedDate: null,
      showFixClockInDialog: false,
      currentSignType: 'on', // 当前补卡类型
      // 考勤统计数据
      attendanceStats: {
        lateArrivalNum: 0,
        leavingEarlyNum: 0,
        notClockingInNum: 0
      },
      // 考勤数据 - 从API获取的数据，格式匹配API返回结构
      attendanceData: {}
    };
  },
  computed: {
    // 选中日期的考勤数据
    selectedDateData() {
      if (!this.selectedDate) return null;
      const dateStr = this.formatDateToString(this.selectedDate);
      const rawData = this.attendanceData[dateStr];
      if (!rawData) return null;

      // 返回处理后的数据，包含格式化的时间
      return {
        ...rawData,
        clockInTime: rawData.clockInTime ? this.formatTime(rawData.clockInTime) : null,
        clockOutTime: rawData.clockOutTime ? this.formatTime(rawData.clockOutTime) : null
      };
    },
    // 格式化选中日期显示
    formatSelectedDate() {
      if (!this.selectedDate) return '';
      return this.formatDateToString(this.selectedDate);
    },
    // 转换考勤数据为日历组件所需格式
    calendarAttendanceData() {
      const calendarData = {};
      Object.keys(this.attendanceData).forEach(dateStr => {
        const record = this.attendanceData[dateStr];
        calendarData[dateStr] = {
          status: record.status === 0 ? 'normal' : 'abnormal'
        };
      });
      return calendarData;
    }
  },
  methods: {
    // 处理日历组件传递的日期选择事件
    onDateSelected(date) {
      console.log('Date selected:', date);
      this.selectedDate = date;
    },
    // 格式化日期为字符串
    formatDateToString(date) {
      if (!date) return '';
      return dayjs(date).format('YYYY-MM-DD');
    },
    // 格式化时间显示（将UTC时间转换为本地时间并提取时间部分）
    formatTime(timeString) {
      if (!timeString) return '';

      // 检查时间字符串格式
      if (timeString.includes(' ')) {
        // 完整的日期时间格式 "2025-06-01 09:00:00"
        // 将UTC时间转换为本地时间，然后提取时间部分
        const localDateTime = toLocalTime(timeString, 'YYYY-MM-DD HH:mm:ss');
        const timePart = localDateTime.split(' ')[1];
        if (timePart) {
          return timePart.substring(0, 5); // 取前5位 "09:00"
        }
        return localDateTime;
      } else {
        // 如果只是时间格式 "09:00:00"，直接提取前5位
        return timeString.substring(0, 5);
      }
    },
    // 获取上班打卡状态样式类
    getOnStatusClass(data) {
      if (data.onStatus === 0 && data.onApprovalStatus === 1) {
        return { 'status-normal': true };
      }

      // 有打卡时间时，根据状态值判断
      return {
        'status-normal': data.onStatus === 0,
        'status-abnormal': data.onStatus !== 0
      };
    },
    // 获取下班打卡状态样式类
    getOffStatusClass(data) {
      if (data.offStatus === 0 && data.offApprovalStatus === 1) {
        return { 'status-normal': true };
      }

      // 有打卡时间时，根据状态值判断
      return {
        'status-normal': data.offStatus === 0,
        'status-abnormal': data.offStatus !== 0
      };
    },
    // 获取上班打卡时间文字样式类
    getClockInTimeClass(data) {
      // 如果没有实际打卡时间，或者状态异常，都显示红色
      return {
        'clock-time-abnormal': (!data.clockInTime || data.onStatus !== 0) && !this.shouldShowNoNeedToClock(data)
      };
    },
    // 获取下班打卡时间文字样式类
    getClockOutTimeClass(data) {
      // 如果没有实际打卡时间，或者状态异常，都显示红色
      return {
        'clock-time-abnormal': (!data.clockOutTime || data.offStatus !== 0) && !this.shouldShowNoNeedToClock(data)
      };
    },
    // 获取上班打卡状态文本
    getOnStatusText(data) {
      // 优先级1：当考勤状态为0（正常），但审批状态为1（审批成功）时，显示"审批通过"
      if (data.onStatus === 0 && data.onApprovalStatus === 1) {
        return this.$t('attendance.approvalPassed');
      }

      // 优先级2：有打卡时间时，根据状态值显示对应文本
      const statusMap = {
        0: this.$t('attendance.normal'),
        1: this.$t('attendance.lateArrival'),
        3: this.$t('attendance.noClockIn')
      };
      return statusMap[data.onStatus] || '';
    },
    // 获取下班打卡状态文本
    getOffStatusText(data) {
      // 优先级1：当考勤状态为0（正常）但审批状态为1（审批成功）时，显示"审批通过"
      if (data.offStatus === 0 && data.offApprovalStatus === 1) {
        return this.$t('attendance.approvalPassed');
      }

      // 优先级2：有打卡时间时，根据状态值显示对应文本
      const statusMap = {
        0: this.$t('attendance.normal'),
        2: this.$t('attendance.leavingEarly'),
        3: this.$t('attendance.noClockOut')
      };
      return statusMap[data.offStatus] || '';
    },
    // 判断是否无需打卡
    shouldShowNoNeedToClock(data) {
      // 主要条件：workFlag=0（假期）时显示"无需打卡"
      if (data.workFlag === 0) {
        return true;
      }
      return false;
    },
    // 获取上班打卡时间显示文本
    getClockInDisplayText(data) {
      const { clockInTime } = data;

      if (this.shouldShowNoNeedToClock(data)) {
        return clockInTime
          ? `${this.$t('attendance.clockInTime')} ${clockInTime}`
          : this.$t('attendance.noNeedToClock');
      }

      return clockInTime
        ? `${this.$t('attendance.clockInTime')} ${clockInTime}`
        : this.$t('attendance.noClockIn');
    },
    // 获取下班打卡时间显示文本
    getClockOutDisplayText(data) {
      const { clockOutTime } = data;

      if (this.shouldShowNoNeedToClock(data)) {
        return clockOutTime
          ? `${this.$t('attendance.clockOutTime')} ${clockOutTime}`
          : this.$t('attendance.noNeedToClock');
      }

      return clockOutTime ? `${this.$t('attendance.clockOutTime')} ${clockOutTime}` : this.$t('attendance.noClockOut');
    },
    // 获取审批状态样式类
    getApprovalStatusClass(approvalStatus) {
      return {
        'action-under-review': approvalStatus === 0,
        // 'action-review-success': approvalStatus === 1,
        'action-review-failed': approvalStatus === 2
      };
    },
    // 获取审批状态文本
    getApprovalBtnText(approvalStatus) {
      const statusMap = {
        0: this.$t('attendance.underReview'),
        // 1: this.$t('attendance.reviewSuccess'),
        2: this.$t('attendance.reviewFailed')
      };
      return statusMap[approvalStatus] || this.$t('attendance.handling') || '';
    },
    // 是否显示打卡审批按钮
    shouldShowApprovalButton(status) {
      return status !== 0;
    },
    // 获取今天的日期
    getTodayDate() {
      return new Date();
    },
    // 打开上班打卡审批弹窗
    openOnApprovalDialog(onApprovalStatus) {
      if (onApprovalStatus === 0 || onApprovalStatus === 2) {
        return;
      }
      this.currentSignType = 'on';
      this.showFixClockInDialog = true;
    },
    // 打开下班打卡审批弹窗
    openOffApprovalDialog(offApprovalStatus) {
      if (offApprovalStatus === 0 || offApprovalStatus === 2) {
        return;
      }
      this.currentSignType = 'off';
      this.showFixClockInDialog = true;
    },
    // 处理补卡提交
    async handleFixClockInSubmit(fixClockInData) {
      console.log('补卡数据:', fixClockInData);

      try {
        // 调用真实的补卡申请API
        await this.submitFixClockIn(fixClockInData);

        // 补卡申请成功后刷新考勤数据
        await this.fetchAttendanceData();

        this.$toast(this.$t('attendance.fixClockInSuccess'));
      } catch (error) {
        console.error('补卡申请失败:', error);
      }
    },
    // 提交补卡申请API
    async submitFixClockIn(data) {
      try {
        console.log('提交补卡申请:', data);

        // 调用真实的补卡申请API
        const response = await submitAttendanceApproval(data);

        console.log('补卡申请响应:', response);
        return response;
      } catch (error) {
        console.error('补卡提交失败:', error);
        throw error;
      }
    },
    // 获取考勤日历数据
    async fetchAttendanceData() {
      try {
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth() + 1;

        const data = await getAttendanceClockRecord({
          year,
          month
        });

        if (data) {
          // 更新统计数据
          this.attendanceStats = {
            lateArrivalNum: data.lateArrivalNum || 0,
            leavingEarlyNum: data.leavingEarlyNum || 0,
            notClockingInNum: data.notClockingInNum || 0
          };

          // 转换记录数据为组件所需格式
          const newAttendanceData = {};
          if (data.records && Array.isArray(data.records)) {
            data.records.forEach(record => {
              newAttendanceData[record.attendanceDate] = record;
            });
          }

          // 合并API数据和mock数据（API数据优先）
          this.attendanceData = { ...this.attendanceData, ...newAttendanceData };
        }
      } catch (error) {
        console.error('获取考勤数据失败:', error);
        // 使用mock数据，不显示错误提示
      }
    }
  },
  async mounted() {
    // 初始化时选择今天的日期
    this.selectedDate = this.getTodayDate();
    // 获取考勤数据
    await this.fetchAttendanceData();
  }
};
</script>

<style scoped lang="scss">
.mt-25 {
  margin-top: 25px;
}
.mt-73 {
  margin-top: 73px;
}
.mt-9 {
  margin-top: 9px;
}
.page {
  background-color: #f1f3f6;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  .section {
    padding: 14px 16px 13px 18px;
    background-color: #ffffff;
    .group {
      line-height: 14.5px;
      height: 14.5px;
      .font {
        font-size: 20px;
        font-family: Montserrat;
        line-height: 14.5px;
        color: #314250;
      }
      .text_2 {
        color: #314250;
        font-size: 20px;
        font-family: PingFang SC;
        line-height: 10px;
      }
      .text {
        line-height: 14px;
      }
    }
    .image {
      width: 28px;
      height: 21px;
    }
    .image_2 {
      width: 17px;
      height: 20px;
    }
    .image_3 {
      width: 34px;
      height: 18px;
    }
  }

  .group_2 {
    .section_3 {
      padding-top: 29px;
      background-color: #ffffff;
      .group_3 {
        padding: 0 24px;
        .text_4 {
          color: #7b8da8;
          font-size: 28px;
          line-height: 20.5px;
        }
          .font_2 {
            font-size: 40px;
            line-height: 29px;
            font-weight: 700;
            color: #1b3155;
          }
          .group_5 {
            display: flex;
            align-items: center;
            padding: 10px 14px 9.5px;
            .font_3 {
              text-align: center;
          }
        }
        .section_4 {
          padding: 46.5px 0 40.5px;
          background-color: #f1f3f6;
          border-radius: 16px;
        }
      }
      .group_6 {
        padding: 0 51px 24px 56px;
        .font_4 {
          font-size: 22.5px;
          line-height: 16px;
          color: #919db3;
        }
        .text_9 {
          margin-left: 4px;
          margin-right: 3px;
          line-height: 16.5px;
        }
        .text_10 {
          margin-left: 4px;
          margin-right: 3px;
        }
        .text_11 {
          line-height: 17px;
        }
        .text_12 {
          margin-left: 4px;
          margin-right: 3px;
          line-height: 17px;
        }
        .text-wrapper {
          width: 45px;
          .text_13 {
            line-height: 16.5px;
          }
        }
      }
    }
  }
  .section_8 {
    margin: 24px 24px 0;
    padding: 28px 22px 17px 25px;
    background-color: #ffffff;
    border-radius: 16px;
    box-shadow: 0px 6px 20px #0000001a;
    .text_38 {
      color: #1b3155;
      font-size: 26px;
      line-height: 19px;
    }
    .text_39 {
      color: #ff1b1b;
      font-size: 24px;
      line-height: 18px;
    }
    .font_7 {
      font-size: 24px;
      line-height: 52px;
      color: #7b8da8;

      // 异常状态时的红色文字
      &.clock-time-abnormal {
        color: #ff1b1b;
      }
    }
    .text_40 {
      margin-top: -9px;
    }
    .text-wrapper_5 {
      margin-right: 2px;
      padding: 12px 20px; // 调整内边距，确保文字不贴边
      background-color: #0074ff;
      border-radius: 49px;
      width: auto; // 宽度自适应内容
      min-width: 180px; // 设置最小宽度
      max-width: 200px; // 设置最大宽度
      height: 70px; // 固定高度
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;

      .text_41 {
        color: #ffffff;
        text-align: center;
        word-wrap: break-word; // 允许单词换行
        word-break: break-word; // 在必要时断开单词
        white-space: normal; // 允许文字换行
        line-height: 1.3; // 设置合适的行间距
        max-width: 100%;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2; // 最多显示两行
        line-clamp: 2; // 标准属性
        -webkit-box-orient: vertical;
      }
    }
  }
  .font_3 {
    font-size: 24px;
    line-height: 23px;
    color: #7b8da8;
  }

  // 状态样式
  .status-normal {
    color: #28a745 !important;
  }

  .status-abnormal {
    color: #dc3545 !important;
  }

  // 操作状态样式
  .action-handling {
    background-color: #007bff !important;
    .text_41 {
      color: #ffffff !important;
    }
  }

  .action-under-review {
    background-color: #6c757d !important;
    .text_41 {
      color: #ffffff !important;
    }
  }

  .action-review-success {
    background-color: #28a745 !important;
    .text_41 {
      color: #ffffff !important;
    }
  }

  .action-review-failed {
    background-color: #dc3545 !important;
    .text_41 {
      color: #ffffff !important;
    }
  }

  // 补卡按钮样式
  .fix-clock-in-button {
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0px 8px 24px #0000002a;
    }

    &:active {
      transform: scale(0.98);
    }
  }

  // 无记录时的样式
  .text_no_record {
    font-size: 24px;
    color: #6c757d;
    text-align: center;
    padding: 40px 0;
  }

  // 详情标题样式
  .text_detail_title {
    color: #1b3155;
    font-size: 24px;
    line-height: 18px;
    font-weight: 600;
  }

  // 区块间距
  .mb-16 {
    margin-bottom: 16px;
  }

  .mb-20 {
    margin-bottom: 20px;
  }
}
</style>
