<template>
  <div class="attendance-calendar">
    <!-- 简洁的日历组件 -->
    <div class="calendar-wrapper">
      <v-calendar
        ref="calendar"
        v-model="selectedDate"
        :attributes="attendanceAttributes"
        @dayclick="onDayClick"
        class="simple-calendar"
        :is-expanded="true"
        :first-day-of-week="1"
        :disablePageSwipe="true"
        :locale="calendarLocale"
      />
    </div>
  </div>
</template>

<script>
import { Calendar } from 'v-calendar'

export default {
  name: 'AttendanceCalendar',
  components: {
    'v-calendar': Calendar
  },
  props: {
    // 从父组件接收考勤数据
    attendanceData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 选中的日期
      selectedDate: null
    }
  },
  computed: {
    // 考勤属性（用于在日历上显示标记）- 参考官网demo的dots逻辑
    attendanceAttributes() {
      // 按状态分组日期
      const normalDates = []
      const abnormalDates = []

      // 遍历考勤数据，按状态分组
      Object.keys(this.attendanceData).forEach(dateStr => {
        const attendance = this.attendanceData[dateStr]
        const date = new Date(dateStr)

        if (attendance.status === 'normal') {
          normalDates.push(date)
        } else {
          abnormalDates.push(date)
        }
      })

      // 返回按官网demo格式的attributes数组
      return [
        {
          key: 'normal-attendance',
          dot: 'blue',
          dates: normalDates
        },
        {
          key: 'abnormal-attendance',
          dot: 'red',
          dates: abnormalDates
        }
      ]
    },
    // 根据当前语言获取日历 locale 配置
    calendarLocale() {
      const currentLang = this.$i18n.locale

      // 支持的主要语言映射
      const supportedLocales = {
        en: 'en-US',
        zh: 'zh-CN',
        es: 'es-ES',
        fr: 'fr-FR',
        sw: 'sw-SW',
      }

      // 优先使用支持的语言，否则兜底到英语
      return supportedLocales[currentLang] || 'en-US';
    }
  },
  methods: {
    /**
     * 处理日期点击事件
     * @param {Object} day - 点击的日期对象
     */
    onDayClick(day) {
      console.log('Day clicked:', day)
      this.selectedDate = day.date
      // 向父组件传递选中的日期
      this.$emit('date-selected', day.date)
    }
  },

  mounted() {
    console.log('Attendance calendar mounted')
    // 默认选择今天
    this.selectedDate = new Date()
  }
}
</script>

<style scoped lang="scss">
.vc-container {
  --blue-600: #0074FF;
  --red-600: #ff0000;
  --gray-500: #919DB3;
  --text-sm: 22.5px;
  --text-lg: 31.5px;
}

// 日历包装器
.calendar-wrapper {
  .simple-calendar {
    width: 100%;
    border: none;
    border-radius: 8px;
    overflow: hidden;
  }
}

// v-calendar 组件样式自定义
::v-deep .vc-container {
  border: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  .vc-arrows-container {
    display: none !important;
  }
  .vc-header {
    margin-bottom: 20px;
    padding: 16px !important;
    background-color: #ffffff !important;

    .vc-title {
      color: #1B3155 !important;
    }
  }

  .vc-weeks {
    padding: 0 29.25px 29.25px !important;
    .vc-weekday {
      //color: #6c757d !important;
      //font-size: 12px !important;
      font-weight: normal !important;
      //padding: 8px 0 !important;
      //text-transform: uppercase !important;
    }

    .vc-day {
      height: 94.5px !important;
      &.is-not-in-month {
        display: none !important;
      }
      .vc-day-content {
        width: 75px !important;
        height: 75px !important;
        border-radius: 50% !important;
        font-size: 31.5px !important;
        font-weight: 500 !important;
        color: #1B3155 !important;
        transition: all 0.2s ease !important;
        z-index: 1;

        &[tabindex="0"] {
          background-color: var(--blue-600) !important;
          border-radius: 50% !important;
          color: white !important;
        }

        &.vc-day-content-selected {
          background-color: #007AFF !important;
          color: white !important;
          font-weight: 600 !important;
        }

        &.vc-day-content-today {
          background-color: #FF9500 !important;
          color: white !important;
          font-weight: 600 !important;
        }
      }
      .vc-day-layer {
        bottom: 12px;
      }
      .vc-dot {
        width: 12px;
        height: 12px;
      }
      &.vc-day-disabled .vc-day-content {
        color: #dee2e6 !important;
        opacity: 0.5 !important;
      }
    }
  }
}
</style>
