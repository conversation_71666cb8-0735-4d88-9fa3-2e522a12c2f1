<template>
  <div class="attendance-container">
    <!-- 动态组件显示区域 -->
    <div class="content-area flex-col">
      <component :is="currentComponent" />
    </div>

    <!-- 底部导航栏 -->
    <div class="flex-col justify-start items-center section_9">
      <div class="flex-row">
        <div
          class="flex-col items-center equal-division-item_4"
          :class="{ active: activeTab === 'attendance' }"
          @click="switchTab('attendance')"
        >
          <img
            class="image_5"
            :src="activeTab === 'attendance' ? require('@/assets/images/entryProcess/nav-clockin-light.png') : require('@/assets/images/entryProcess/nav-clockin-gray.png')"
            alt="clockin"
          />
          <span class="font_8 mt-6" :class="activeTab === 'attendance' ? 'text_43' : 'text_42'">{{ $t('attendance.clockIn') }}</span>
        </div>
        <div
          class="flex-col items-center equal-division-item_5"
          :class="{ active: activeTab === 'statistics' }"
          @click="switchTab('statistics')"
        >
          <img
            class="image_5"
            :src="activeTab === 'statistics' ? require('@/assets/images/entryProcess/nav-statistics-light.png') : require('@/assets/images/entryProcess/nav-statistics-gray.png')"
            alt="statistics"
          />
          <span class="font_8 mt-9" :class="activeTab === 'statistics' ? 'text_43' : 'text_42'">{{ $t('title.statistics') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AttendanceView from './attendance.vue'
import StatisticsView from './statistics.vue'

export default {
  name: 'AttendanceIndex',
  components: {
    AttendanceView,
    StatisticsView
  },
  data() {
    return {
      activeTab: 'attendance' // 默认显示打卡页面
    }
  },
  computed: {
    currentComponent() {
      return this.activeTab === 'attendance' ? 'AttendanceView' : 'StatisticsView'
    }
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab
    }
  }
}
</script>

<style scoped lang="scss">
.attendance-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f1f3f6;
}

.content-area {
  flex: 1;
  overflow: hidden;
}

.mt-9 {
  margin-top: 9px;
}

.section_9 {
  padding: 8.5px 0 29px;
  background-color: #ffffff;
  box-shadow: 0px -4px 20px #0000000a;
  border-top: solid 1px #d7d7d7;

  .equal-division-item_4,
  .equal-division-item_5 {
    padding: 10.5px 0 9.5px;
    width: 300px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  .equal-division-item_5 {
    padding: 10.5px 0 7.5px;
  }

  .image_5 {
    width: 46px;
    height: 46px;
  }

  .font_8 {
    font-size: 20px;
    line-height: 14.5px;
  }

  .text_42 {
    color: #c4cad5;
    line-height: 15px;
  }

  .text_43 {
    color: #0074ff;
    line-height: 14.5px;
  }
}
</style>
