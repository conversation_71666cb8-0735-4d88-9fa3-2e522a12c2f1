<template>
  <div class="unsupported-container">
    <div class="content">
      <p class="message">This device is not supported. Please use an Apple phone to access.</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Unsupported'
}
</script>

<style lang="scss" scoped>
.unsupported-container {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;

  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0 36px;

    .warning-icon {
      width: 80px;
      height: 80px;
      margin-bottom: 24px;
    }

    .message {
      display: inline-block;
      max-width: 85%;
      font-size: 32px;
      color: #333;
      line-height: 1.5;
      text-align: center;
    }
  }
}
</style>
