<template>
  <div class="native-demo">
    <public-header :pageName="`nativeDemo`" />
    <ul class="native-list">
      <li v-for="(fun, index) in nativeList" :key="index">
        <div class="title" v-text="`1. 原生方法名: ${index}`"></div>
        <div class="des" v-text="`2. 功能说明: ${fun.funtionDes}`"></div>
        <div class="action" @click="commonAction(fun, index)">
          <div>3. </div>
          <van-uploader v-if="fun.type === 'inputFile'" :after-read="afterRead"/>
          <div v-else class="click">点击调用</div>
        </div>
        <div class="result">
          <div>4. 返回结果:</div>
          <div class="res" v-if="fun.value" v-text="fun.value"></div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import {
  getGpsInfo,
  getWifiInfo,
  startRecord,
  stopRecord,
  startSystemCapture,
  getAppConfig,
  requestPermissions,
  checkPermissions,
  showToast, getCallbackName, loginEM, uploadData, finishHtmlPage, sendCode
} from '@/api/native';
import { decode } from 'js-base64';
import { Uploader } from "vant";
import { PERMISSION_ENUMS } from "@/enums";

export default {
  name: 'nativeDemo',
  components: {
    VanUploader: Uploader
  },
  data() {
    return {
      nativeList: {
        getGpsInfo: {
          funtionDes: '获取当前位置的gps(所有包都支持)',
          action: getGpsInfo,
          value: ''
        },
        getWifiInfo: {
          funtionDes: '当前wifi(所有包都支持)',
          action: getWifiInfo,
          value: ''
        },
        startSystemCapture: {
          funtionDes: '拍照(除了ebx外，所有包都支持)',
          action: async() => {
            const res = await checkPermissions({
              permissions: [PERMISSION_ENUMS.CAMERA]
            })
            console.log('🎉 ~ file: voiceCollectionMixins.js ~ line: 37 ~ res: ', res);
            if (res.deny?.length) {
              this.$toast(this.$t('toastMsg.noPermission'));
              setTimeout(() => {
                requestPermissions({
                  permissions: [PERMISSION_ENUMS.CAMERA]
                }).then(res => {
                  if (res.deny === '[]'|| (Array.isArray(res.deny) && res.deny.length === 0)) {
                    this.hasPermission = true;
                  }
                }).catch((err) => {
                  console.log('🎉 ~ file: nativeDemo.vue ~ line: 74 ~ err: ', err);
                })
              }, 2000);
              return;
            }
            return startSystemCapture();
          },
          value: ''
        },
        /*getFilePhotos: {
          funtionDes: '选取相册图片(isShowAlbum方法返回为true的才支持, ebx不支持)',
          action: getFilePhotos,
          value: ''
        },
        isShowAlbum: {
          funtionDes: '是否显示相册(安卓10以上才会返回true, 目前只有加纳, 肯尼亚没有)',
          action: isShowAlbum,
          value: ''
        },*/
        startRecord: {
          funtionDes: '开始录音',
          action: () => startRecord({
            duration_min: 5000,
            duration_max: 10000,
            stop_callback: getCallbackName('stopRecord')
          }),
          value: ''
        },
        stopRecord: {
          funtionDes: '结束录音',
          action: stopRecord,
          value: ''
        },
        /*uploadDataCollect: {
          funtionDes: '大数据数据收集（目前只有坦桑的安卓包才支持, ebx不支持）',
          action: uploadDataCollect,
          value: ''
        },*/
        checkPermissions: {
          funtionDes: '检查权限',
          action: () => checkPermissions({
          permissions: Object.values(PERMISSION_ENUMS)
        }),
          value: ''
        },
        requestPermissions: {
          funtionDes: '获取手机权限',
          action: () => requestPermissions({
            permissions: Object.values(PERMISSION_ENUMS)
          }),
          value: ''
        },
        无需: {
          type: 'inputFile',
          funtionDes: '拦截input file',
          value: ''
        },
        getAppConfig: {
          funtionDes: '进件流程中的获取app设置（只有加纳的支持H5进件的安卓包支持）',
          action: getAppConfig,
          value: ''
        },
        loginEM: {
          funtionDes: '登录到员工管理后告知客户端登录信息',
          action: () => loginEM({
            uid: '123123',
            token: '123412312'
          }),
          value: ''
        },
        uploadData: {
          funtionDes: '数据采集&上报',
          action: uploadData,
          value: ''
        },
        finishHtmlPage: {
          funtionDes: '关闭员工管理页面',
          action: finishHtmlPage,
          value: ''
        },
        sendCode: {
          funtionDes: '发送code刷新token',
          action: () => sendCode({
            code: '123123',
            from: 'cetus'
          }),
          value: ''
        },
      },
      acceptType: 'application/msword|application/vnd.openxmlformats-officedocument.wordprocessingml.document|application/vnd.ms-excel|application/vnd.openxmlformats-officedocument.spreadsheetml.sheet|application/pdf|image/*',
      productSource: '',
    };
  },

  mounted() {
    this.productSource = localStorage.getItem('productSource');
    setTimeout(() => {
      window.nativeBackCallback = function() {
        console.log('nativeBackCallback');
        // 1 原生不处理返回键  0 原生处理返回键
        return 0;
      }
    }, 100);
  },

  methods: {
    commonAction(fun, name) {
      const ayncArr = ['getWifiInfo', 'getGpsInfo', 'getFilePhotos', 'checkPermissions', 'requestPermissions', 'getAppConfig'];
      if (ayncArr.includes(name)) {
        const action = this.nativeList[name].action;
        action().then(res => {
          console.log('🎉 ~ file: nativeDemo.vue ~ line: 194 ~ res: ', res);
          if (typeof res === 'boolean') {
            // 转字符串显示
            res = res + ''
          }
          this.nativeList[name].value = JSON.stringify(res);
        }).catch(res => {
          showToast(res.message);
          console.log(res)
        })
        return;
      } else if (name === 'startRecord') {
        const action = this.nativeList[name].action;
        // eslint-disable-next-line promise/catch-or-return
        action().then(res => {
          console.log('🎉 ~ file: nativeDemo.vue ~ line: 209 ~ startRecord res: ', res);
          // 1:开始录音,2:录音结束,3:录音失败-无权限 4:录音失败-其他错误
          if (res.status === 3) {
            requestPermissions({
              permissions: [PERMISSION_ENUMS.RECORD_AUDIO]
            })
          }
          if (res.status === 2) {
            this.$toast(res.msg)
          }
          if (res.status === 0) {
            console.log('🎉 ~ file: nativeDemo.vue ~ line: 220 ~ : 开始录音');
          }
        })
      } else {
        const action = this.nativeList[name].action;
        action();
      }
      // 返回结果是文件
      const fileArr = ['stopRecord', 'startSystemCapture'];
      if (fileArr.includes(name)) {
        const action = this.nativeList[name].action;
        /**
         * {
         *     res
         *     "fileType": "",
         *     "fileSize": "",
         *     "fileName": "",
         *     "filePath": "",
         *     "base64": ""
         * }
         */
        action().then(res => {
          console.log(`🎉 ~ file: nativeDemo.vue ~ line: 224 ~ ${name} res: `, res);
          if (name ==='stopRecord') {
            res = res.file;
          }

          // Base64 转 Blob 的工具函数
          function base64ToBlob(base64, mimeType) {
            const byteCharacters = atob(base64); // 解码 Base64
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
          }

          // 将 Base64 数据转换为 Blob
          const blob = base64ToBlob(res.base64, res.fileType);
          // 创建 FormData 并上传
          const formData = new FormData();
          formData.append('file', blob, res.fileName);
          // uploadFile(formData)

          // this.nativeList[name].value = file?.file?.name;
        }).catch(err => {
          console.error(err)
        })
      }
      if (name === 'activateTradeBigdata') {
        if (!this.nativeList.getBatchNo.value) {
          return showToast('请先获取getBatchNo')
        }
        if (!this.nativeList.getCustId.value) {
          return showToast('请先获取getCustId')
        }
        window.setActivateTradeResult = (res) => {
          const result = JSON.parse(decode(res))
          console.log('setActivateTradeResult', result)
          this.nativeList.activateTradeBigdata.value = result.activateState
        }
      }
      if (name === 'saveUserData') {
        // eslint-disable-next-line
        saveUserData({
          id: '693880346613121111',
          applyNo: '693880346055278610',
          uid: '189430393',
          reqChannel: 'newloan',
          custId: '0',
          crdStage: null,
          createTime: '2024-03-29 09:52:46',
          updateTime: '2024-03-29 09:52:46',
          idCardType: null,
          idCard: null,
          firstName: null,
          middleName: null,
          surName: null,
          gender: null,
          birthday: null,
          educationType: null,
          workStatus: null,
          creditStage: null,
          mobile: null,
          isCopyName: null,
          monthlyIncome: null,
          maritalStatus: null,
          otherIdType: null,
          otherIdNumber: null,
          kycResult: null,
          ocrTimes: null,
          ocrResult: null,
          createType: null
        })
      }
      if (name ==='requestPermissions') {
        // 获取日历的写和读的权限
        requestPermissions('android.permission.READ_SMS|android.permission.READ_CALL_LOG|android.permission.READ_PHONE_STATE|android.permission.READ_CONTACTS');
      }
      if (name === 'checkPermissionss') {
        checkPermissions('android.permission.READ_SMS|android.permission.READ_CALL_LOG|android.permission.READ_PHONE_STATE|android.permission.READ_CONTACTS', 'checkPermissionsCallBack')
        window.checkPermissionsCallBack = (res) => {
          this.nativeList.checkPermissionss.value = JSON.parse(decode(res));
        }
        return
      }
      // ebx同步请求
      const syncArr = ['isSupportGoogleService', 'toFaceAuthActivity', 'getLoginStatus'];
      if (syncArr.includes(name)) {
        this.nativeList[name].value = this.nativeList[name].action();
      }
    },
    afterRead(file) {
      console.log('🎉 ~ file: nativeDemo.vue ~ line: 377 ~ file: ', file);
      showToast('file name: ' + file?.file?.name);
    },
  }
};
</script>

<style lang="scss" scoped>
.native-demo{
  height: calc(100vh - 112px);
  .native-list{
    margin-top: 112px;
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 50px;
    height: 100%;
    overflow: scroll;
    li{
      padding: 30px;
      border: 2px solid #c4c3c3;
      border-radius: 28px;
      line-height: 40px;
      margin-bottom: 20px;
      div{
        font-size: 24px;
      }
      .action{
        display: flex;
        align-items: center;
        .click{
          padding: 5px;
          color: #fff;
          background-color: #07c160;
          border: 1px solid #07c160;
        }
      }
      .result{
        .res{
          border: 2px solid #ababab;
          border-radius: 10px;
          padding: 10px;
          word-break: break-all;
        }
      }
    }
  }
}
</style>
