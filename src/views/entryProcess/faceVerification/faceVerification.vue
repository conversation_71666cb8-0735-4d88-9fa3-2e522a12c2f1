<template>
  <div class="face-verification">
    <public-header
      :pageName="$t('title.faceVerification')"
      headerColor="#fff"
    />
    <div class="content">
      <div class="tips-1 flex-col items-center self-stretch mt-24 mb-42">
        <span class="text_4">{{ $t('faceVerification.scanningFaces') }}</span>
        <span class="text_5 mt-22">{{ $t('faceVerification.keep') }}</span>
      </div>
      <img class="example" :src="imageUrl" />
      <ul class="note-list">
        <li v-for="notice in noticeList" :key="notice.text">
          <img :src="notice.img" alt="">
          <div class="text" v-text="notice.text"></div>
        </li>
      </ul>
      <div class="note-text-list">
        <div>{{ $t('faceVerification.note') }}: </div>
        <p v-for="item in noticeTextList" :key="item">{{item}}</p>
      </div>
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        capture="user"
        class="hidden-input"
        @change="handleFileChange"
      />
      <sticky-footer-container>
        <template #content>
          <CButton v-debounce="startFaceVerification" :name="`${$t('faceVerification.photograph')}`">
            <template #icon>
              <img src="@/assets/images/entryProcess/faceVerification/ic_faceid_button-icon.svg" alt="">
            </template>
          </CButton>
        </template>
      </sticky-footer-container>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import { startSystemCapture, requestPermissions, checkPermissions } from "@/api/native";
import { cacheJumpPage } from "@/utils/tools";
import StickyFooterContainer from '@/components/sticky-footer-container.vue'
import { base64ToBlob } from "@/utils/file";
import { compressImage } from "@/utils/image";
import { PERMISSION_ENUMS } from "@/enums";
import CButton from "@/components/c-button.vue";

let tmpFromPage = "";

export default {
  name: 'FaceVerification',
  components: {
    StickyFooterContainer,
    CButton
  },
  beforeRouteEnter(to, from, next) {
    tmpFromPage = from.name
    next()
  },
  data() {
    return {
      formData: {
        facePhoto: "", //图片亚马逊地址
      },
      facePhotoPreview: "", //图片亚马逊地址
      noticeList: [{
        img: require("@/assets/images/entryProcess/faceVerification/ic_faceid_note1.png"),
        text: ''
      }, {
        img: require("@/assets/images/entryProcess/faceVerification/ic_faceid_note2.png"),
        text: ''
      }, {
        img: require("@/assets/images/entryProcess/faceVerification/ic_faceid_note3.png"),
        text: ''
      }, {
        img: require("@/assets/images/entryProcess/faceVerification/ic_faceid_note4.png"),
        text: ''
      }],
      noticeTextList: [
        this.$t('faceVerification.noteTextList.0'),
        this.$t('faceVerification.noteTextList.1'),
        this.$t('faceVerification.noteTextList.2'),
        this.$t('faceVerification.noteTextList.3'),
      ],
      isStartFaceVerification: false,
      routeName: '',
    }
  },
  computed: {
    imageUrl() {
      return this.facePhotoPreview || require('@/assets/images/entryProcess/faceVerification/faceVerificationExample.png')
    },
  },
  created() {
    this.routeName = this.$route.name
    this.changeLanguage();
  },
  mounted() {
    this.cacheJumpPage();
  },
  methods: {
    ...mapActions(['uploadFile', 'updateForm']),
    cacheJumpPage() {
      cacheJumpPage(this.routeName)
    },
    changeLanguage() {
      this.noticeList[0].text = this.$t('faceVerification.noticeList.0');
      this.noticeList[1].text = this.$t('faceVerification.noticeList.1');
      this.noticeList[2].text = this.$t('faceVerification.noticeList.2');
      this.noticeList[3].text = this.$t('faceVerification.noticeList.3');
    },
    async handleFileUpload(file) {
      try {
        this.isStartFaceVerification = true;
        const data = await this.uploadFile(file);
        if (data.absoluteUrl) {
          this.formData.facePhoto = data.relativeUrl;
          this.facePhotoPreview = data.absoluteUrl;
          await this.updateForm(this.formData);
          this.$router.push('/voiceCollection')
        }
      } finally {
        this.isStartFaceVerification = false;
      }
    },
    async handleFileChange(event) {
      if (!event.target.files || !event.target.files[0]) return;

      try {
        const file = event.target.files[0];
        const compressedFile = await compressImage(file);

        this.isStartFaceVerification = true;
        this.handleFileUpload(compressedFile);
      } catch (err) {
        this.$toast(err.msg || err.message);
        console.log('File upload error:', err);
      } finally {
        this.isStartFaceVerification = false;
        event.target.value = '';
      }
    },
    async startFaceVerification() {
      console.log('🎉 ~ file: faceVerification.vue ~ line: 166 ~ this.isStartFaceVerification: ', this.isStartFaceVerification);
      if (this.isStartFaceVerification) return;
      try {
        const res = await checkPermissions({
          permissions: [PERMISSION_ENUMS.CAMERA]
        })
        if (res.deny?.length) {
          this.$toast(this.$t('toastMsg.noPermission'));
          setTimeout(() => {
            requestPermissions({
              permissions: [PERMISSION_ENUMS.CAMERA]
            }).then(res => {
              if (res.deny?.length) {
                this.$toast(this.$t('toastMsg.noPermission'));
              }
            }).catch((err) => {
              console.log('🎉 ~ file: faceVerification.vue ~ line: 186 ~ err: ', err);
            })
          }, 1000);
          return;
        }
      } catch (err) {
        console.log('🎉 ~ file: faceVerification.vue ~ line: 192 ~ err: ', err);
        return;
      }

      try {
        const res = await startSystemCapture();
        console.log('🎉 ~ file: faceVerification.vue ~ line: 198 ~ res: ', res);
        if (!res.base64) return;
        const blob = base64ToBlob(res.base64, res.fileType);
        const file = new File([blob], res.fileName || 'face.jpg', { type: blob.type });
        this.handleFileUpload(file);
      } catch (err) {
        this.$toast(err.msg || err.message);
      } finally {
        this.isStartFaceVerification = false;
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.face-verification {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .content{
    display: flex;
    flex-direction: column;
    height: calc(100% - 120px);
    width: 100%;
    box-sizing: border-box;
    padding: 0 36px;
    overflow: scroll;

    .example{
      width: 370px;
      height: 370px;
      margin: 0 auto;
      border-radius: 50%;
    }
  }

  .text_4 {
    color: #1b3155;
    font-size: 48px;
    font-weight: 700;
    line-height: 46.5px;
  }

  .text_5 {
    color: #919db3;
    font-size: 28px;
    line-height: 27.5px;
  }

  .note-list{
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-top: 44px;

    li {
      flex: 1;
    }

    img{
      width: 72px;
      height: 72px;
      margin: 0 auto 6px auto;
    }

    .text{
      color: #919DB3;
      text-align: center;
      font-size: 22px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }

  .note-text-list {
    flex: 1;
    margin-top: 36px;
    margin-left: 38px;
    margin-bottom: 66px;
    color: #7B8DA8;
    font-size: 24px;
    font-weight: 400;
    line-height: normal;
  }

  .sticky-footer-container {
    img {
      width: 40px;
      height: 40px;
    }
  }

  .hidden-input {
    display: none;
  }
}
</style>
