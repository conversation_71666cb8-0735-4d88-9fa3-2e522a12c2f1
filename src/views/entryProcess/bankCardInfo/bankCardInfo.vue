<template>
  <div class="bankcard-info-container">
    <public-header :pageName="$t('title.bankcardInfo')" :fromRouteName="fromRouteName" headerColor="#EFF3F6">
    </public-header>
    <form-page-header/>
    <div class="credit-index">
      <ValidationObserver slim ref="form">
        <form
          class="bankcardForm"
          @focus.capture="focusCapture"
          @click.capture="clickCapture"
          novalidate
        >
          <!--信息填写 1 start-->
          <div class="formBox">

            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_1"
              :name="$t('bankcardInfo.bankNameLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  :is-required="true"
                  :id="'emailBox'"
                  v-model="formData.bankName"
                  :label="$t('bankcardInfo.bankNameUpper')"
                  maxlength="100"
                />
              </div>
            </ValidationProvider>

            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_2"
              :name="$t('bankcardInfo.bankBranchLower')"
              rules="min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  :is-required="failed"
                  :id="'bankBranchBox'"
                  v-model="formData.bankBranch"
                  :label="$t('bankcardInfo.bankBranchUpper')"
                  maxlength="100"
                />
              </div>
            </ValidationProvider>

            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_3"
              :name="$t('bankcardInfo.acctNumberLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  :is-required="true"
                  :id="'acctNumberBox'"
                  v-model="formData.acctNumber"
                  :label="$t('bankcardInfo.acctNumberUpper')"
                  maxlength="100"
                />
              </div>
            </ValidationProvider>
          </div>
          <sticky-footer-container>
            <template #content>
              <footer-note/>
              <CButton :name="$t('common.Next')" v-debounce="handleGoNext"/>
            </template>
          </sticky-footer-container>
        </form>
      </ValidationObserver>
    </div>
  </div>
</template>

<script>
import formPageHeader from '@/components/form-page-header.vue'
import PalmInput from "@/components/palm-input.vue";
import StickyFooterContainer from '@/components/sticky-footer-container.vue'
import CButton from "@/components/c-button.vue";
import { jumpIndex } from "@/utils/anchorPoint.js";
import { ValidationObserver, ValidationProvider } from "vee-validate";
import { cacheJumpPage } from '@/utils/tools.js'
import FooterNote from "@/components/FooterNote.vue";
import { mapActions, mapState } from "vuex";
import pick from "lodash/pick";
import { virtualKeyboardMixin } from "@/utils/virtualKeyboard";

let tmpFromPage = "";
export default {
  name: "bankCardInfo",
  mixins: [virtualKeyboardMixin],
  components: {
    FooterNote,
    formPageHeader,
    PalmInput,
    ValidationProvider,
    ValidationObserver,
    CButton,
    StickyFooterContainer,

  },
  data() {
    return {
      // 虚拟键盘处理配置
      virtualKeyboardOptions: {
        targetSelector: '.bankcardForm',
      },
      phone: '',
      deviceId: '',
      batchNo: '',
      reqChannel: '',
      uid: '',
      enterTime: '',
      leaveTime: '',
      formData: {
        bankName: '',
        bankBranch: '',
        acctNumber: '',
      },
      files: {
        file1: {},
        file2: {},
      },
      languageStatus: '', // 后台返回的key
      key: {}, // 用于提交后台的值
      emailError: false,
      //把每个表单中的验证取名的testName 组成一个数组
      testName: [
        { name: "gender", index: "1" },
        { name: "email", index: "2" },
        { name: "educationType", index: "3" },
        { name: "educationMajor", index: "4" },
        { name: "contactAddress", index: "6" },
        { name: "maritalStatus", index: "7" },
        { name: "numberOfChildren", index: "8" },
        { name: "lastCompanyName", index: "9" },
        { name: "lastJobPosition", index: "10" },
      ],

      /*-------------------------------------------供选择的枚举值 start-----------------------------------------------------*/
      idTypeList: [
        { label: "1", value: "BVN" },
        { label: "2", value: "National ID" },
      ],
      martialStatusArray: [
        // Single/Married/Seperated/Divorced/Widowed
        { name: "1", value: "Single" },
        { name: "2", value: "Married" },
        { name: "3", value: "Seperated" },
        { name: "4", value: "Divorced" },
        { name: "5", value: "Widowed" },
      ],
      fromBackEnd: false, //由后台带出标志位

      productSource: '',
      stepOneVisible: false,
      creditPopVisible: false,
      custId: '',
      applyNo: '',
      crdStage: '',
      bubbleTip: '',
      fromRouteName: '',
      routeName: '',
      nativePageName: 'com.xloan.cash.ui.applycredit.CreditStep1Activity', // 原生页面名称, 默认紧急联系人前置的个人资料页面
      packageName: '',
      birthdayView: '',
      // 缓存用户填写的个人信息
      currentDate: new Date()
    };
  },
  computed: {
    ...mapState(['userInfo']),
  },
  beforeRouteEnter(to, from, next) {
    tmpFromPage = from.name
    next()
  },
  async created() {
    this.setInitData()

    console.log("bankCardInfo----fromRouteName", tmpFromPage);
    this.fromRouteName = tmpFromPage;
    this.$store.commit("SET_FROMROUTENAME", tmpFromPage);
  },
  mounted() {
    this.cacheJumpPage()
  },
  beforeDestroy() {
    this.leaveTime = Date.now();
    // const diffTime = this.leaveTime - this.enterTime
    this.handleLocalData()
  },
  methods: {
    ...mapActions(['fetchInviteInfo', 'submitForm']),
    cacheJumpPage() {
      cacheJumpPage(this.routeName)
    },
    backFun() {
    },
    /**
     * 处理缓存数据
     * 没有数据则缓存，有则赋值
     */
    setInitData() {
      if (this.userInfo) {
        const keys = Object.keys(this.formData);
        keys.forEach((key) => {
          const value = this.userInfo[key]?.toString();
          if (value) {
            this.formData[key] = value;
          }
        });
      }
    },
    handleLocalData() {
      localStorage.setItem("uid", this.uid)
      const localData = Object.assign({}, { formData: this.formData }, { key: this.key }, { languageStatus: this.languageStatus })
      console.log('localData', localData)
      this.$store.commit('SET_contactInfoObj', {
        key: `contactInfoObj${this.uid}`,
        value: localData
      });
    },
    //全局焦点事件捕获
    focusCapture() {
      if (this.fromBackEnd) {
        this.fromBackEnd = false
      }
    },
    //全局点击事件捕获
    clickCapture() {
      if (this.fromBackEnd) {
        this.fromBackEnd = false
      }
    },
    //前往下一步
    handleGoNext() {
      const _this = this;
      _this.$refs.form.validateWithInfo().then(({ isValid, errors }) => {
        //如果表单验证通过
        if (isValid) {
          this.submit()
        } else {
          // 找到第一个错误的字段
          const firstError = Object.keys(errors).filter(key => errors[key].length)[0];
          const fieldIdIndexMap = {
            [this.$t('bankcardInfo.bankNameLower')]: '1',
            [this.$t('bankcardInfo.bankBranchLower')]: '2',
            [this.$t('bankcardInfo.acctNumberLower')]: '3'
          };

          const elementId = fieldIdIndexMap[firstError];
          if (elementId) {
            // 根据错误字段找到对应的 DOM 元素 ID
            jumpIndex(elementId);
          }
        }
      })
      .catch((e) => {
        console.log(e);
      });
    },
    async submit() {
      const keys = [
        'id',
        'firstName',
        'lastName',
        'gender',
        'birthday',
        'education',
        'school',
        'educationMajor',
        'contactAddress',
        'maritalStatus',
        'childrenNum',
        'lastCompanyName',
        'lastJobPosition',
        'idType',
        'idNumber',
        'hasIdCard',
        'idCardFrontPhoto',
        'idCardBackPhoto',
        'facePhoto',
        'voicePrint',
        'emergencyContactRelation',
        'emergencyContactName',
        'emergencyContactPhone',
        'guarantorContactRelation',
        'guarantorContactName',
        'guarantorContactPhone',
        'bankName',
        'bankBranch',
        'acctNumber'
      ];
      try {
        const formData = pick(this.userInfo, keys);
        await this.submitForm({
          ...formData,
          ...this.formData,
        });
        this.$router.push('/result');
      } catch (err) {
        console.log('🎉 ~ file: bankCardInfo.vue ~ line: 328 ~ err: ', err);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.bankcard-info-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  background: #EFF3F6;
}

.credit-index {
  flex: 1;
  //height: 100%;
  padding-top: 38px;
  box-sizing: border-box;
  background: #fff;
  border-top-left-radius: 36px;
  border-top-right-radius: 36px;

  .bankcardForm {
    display: flex;
    flex-direction: column;
    height: 100%;
    .c-button {
      @include themify() {
        background: themed("color");
      }
      box-shadow: none;
      margin-top: 35px;
    }
  }

  .formBox {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 45px;
    box-sizing: border-box;

    & > div {
      margin-left: 24px;
      margin-right: 24px;
    }
  }

  .fill_item {
    margin-bottom: 18px;
    text-align: left;
    position: relative;
  }

  .select_one {
    margin-top: 0;
  }

  .select_wrap {
    position: relative;
  }

  /*输入框的样式修改*/
  /*设置输入框提示语的样式*/
  .fill_item input::placeholder {
    font-size: 32px;
    color: #919db3;
  }

  .fill_item textarea::placeholder {
    font-size: 32px;
    color: #919db3;
  }

  /* -----------------下拉选择菜单样式 --------------------*/
  .sticky-footer-container {
    background: #ffffff;
  }

}
</style>
