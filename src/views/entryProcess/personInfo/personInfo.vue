<template>
  <div class="personinfo-container">
    <public-header :pageName="$t('title.basicInfo')" :selfBackFlag="true" :backFun="backFun" :fromRouteName="fromRouteName" headerColor="#EFF3F6">
    </public-header>
    <form-page-header/>
    <div class="credit-index">
      <ValidationObserver slim ref="form">
        <form
          class="personForm"
          @focus.capture="focusCapture"
          @click.capture="clickCapture"
          novalidate
        >
          <!--信息填写 1 start-->
          <div class="formBox">
            <!-- 性别 gender -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_1"
              :name="$t('personInfo.genderLower')"
              rules="requiredSelect|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="select_wrap gender_box"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <DropDownList
                  :dataList="genderList"
                  :label="$t('personInfo.genderUpper')"
                  labelProperty="name"
                  @change="genderChange"
                  v-model="formData.gender"
                  :isRequired="true"
                />
              </div>
            </ValidationProvider>

            <!-- 生日 -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_2"
              :name="$t('personInfo.birthLower')"
              rules="requiredSelect|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="select_wrap"
                :class="{ error_input_border: failed || fromBackEnd }"
                :data-error="errors[0]"
              >
                <dropDownCalendar
                  :label="$t('personInfo.birthUpper')"
                  labelProperty="name"
                  @change="birthdayChange"
                  v-model="birthdayView"
                  :isRequired="true"
                  :currentDate="currentDate"
                />
              </div>
            </ValidationProvider>

            <!-- 学历 -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_3"
              :name="$t('personInfo.educationalLower')"
              rules="requiredSelect|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="select_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <DropDownList
                  :dataList="educationList"
                  :label="$t('personInfo.educationalUpper')"
                  labelProperty="name"
                  @change="educationChange($event)"
                  v-model="formData.education"
                  :isRequired="true"
                />
              </div>
            </ValidationProvider>

            <!-- 学校名称 -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_4"
              :name="$t('personInfo.schoolLower')"
              rules="min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed}"
                :data-error="errors[0]"
              >
                <palm-input
                  id="schoolInput"
                  v-model="formData.school"
                  :label="$t('personInfo.schoolUpper')"
                  maxlength="100"
                  type="text"
                  :isRequired="false"
                />
              </div>
            </ValidationProvider>

            <!-- 专业 -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_5"
              :name="$t('personInfo.majorLower')"
              rules="min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed}"
                :data-error="errors[0]"
              >
                <palm-input
                  id="majorInput"
                  v-model="formData.educationMajor"
                  :label="$t('personInfo.majorUpper')"
                  maxlength="100"
                  type="text"
                  :isRequired="false"
                />
              </div>
            </ValidationProvider>

            <!-- 居住地址 -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_6"
              :name="$t('personInfo.addressLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed}"
                :data-error="errors[0]"
              >
                <palm-input
                  id="contactAddressInput"
                  v-model="formData.contactAddress"
                  :label="$t('personInfo.addressUpper')"
                  maxlength="100"
                  type="text"
                  :isRequired="true"
                />
              </div>
            </ValidationProvider>

            <!-- 婚姻状况 -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_7"
              :name="$t('personInfo.maritalStatusLower')"
              rules="requiredSelect|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="select_wrap"
                :class="{ error_input_border: failed || fromBackEnd }"
                :data-error="errors[0]"
              >
                <DropDownList
                  :id="'martialStatusBox'"
                  :dataList="martialStatusArray"
                  :label="$t('personInfo.maritalStatusUpper')"
                  labelProperty="name"
                  @change="marticalChange($event)"
                  v-model="formData.maritalStatus"
                  :isRequired="true"
                />
              </div>
            </ValidationProvider>

            <!-- 孩子个数 -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_8"
              :name="$t('personInfo.childrenNumLower')"
              rules="required|numberTest|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed}"
                :data-error="errors[0]"
              >
                <palm-input
                  id="childrenNumInput"
                  v-model="formData.childrenNum"
                  :label="$t('personInfo.childrenNumUpper')"
                  maxlength="2"
                  type="text"
                  @input="handleInputChildren"
                  :isRequired="true"
                />
              </div>
            </ValidationProvider>

            <!-- 上一家公司的名称 -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_9"
              :name="$t('personInfo.lastCompanyNameLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed}"
                :data-error="errors[0]"
              >
                <palm-input
                  id="lastCompanyNameInput"
                  v-model="formData.lastCompanyName"
                  :label="$t('personInfo.lastCompanyNameUpper')"
                  maxlength="100"
                  type="text"
                  :isRequired="true"
                />
              </div>
            </ValidationProvider>

            <!-- 上一份工作职位 -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_10"
              :name="$t('personInfo.lastJobPositionLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed}"
                :data-error="errors[0]"
              >
                <palm-input
                  id="lastJobPositionInput"
                  v-model="formData.lastJobPosition"
                  :label="$t('personInfo.lastJobPositionUpper')"
                  maxlength="100"
                  type="text"
                  :isRequired="true"
                />
              </div>
            </ValidationProvider>
          </div>
          <sticky-footer-container>
            <template #content>
              <footer-note />
              <CButton :name="$t('common.Next')" v-debounce="handleGoNext"/>
            </template>
          </sticky-footer-container>
        </form>
      </ValidationObserver>
    </div>
  </div>
</template>

<script>
import formPageHeader from '@/components/form-page-header.vue'
import DropDownList from "@/components/dropDownList.vue";
import dropDownCalendar from "@/components/dropDownCalendar.vue";
import PalmInput from "@/components/palm-input.vue";
import StickyFooterContainer from '@/components/sticky-footer-container.vue'
import CButton from "@/components/c-button.vue";
import { jumpIndex } from "@/utils/anchorPoint.js";
import { ValidationObserver, ValidationProvider } from "vee-validate";
import { cacheJumpPage, dateFormatDateMonthYear, dateFormatYearMonthDate } from '@/utils/tools.js'
import FooterNote from "@/components/FooterNote.vue";
import { mapActions, mapState } from "vuex";
import { virtualKeyboardMixin } from '@/utils/virtualKeyboard.js';

export default {
  name: "personInfo",
  mixins: [virtualKeyboardMixin],
  components: {
    FooterNote,
    formPageHeader,
    DropDownList,
    PalmInput,
    ValidationProvider,
    ValidationObserver,
    dropDownCalendar,
    CButton,
    StickyFooterContainer
  },
  data() {
    return {
      // 虚拟键盘处理配置
      virtualKeyboardOptions: {
        targetSelector: '.credit-index',
      },
      phone: '',
      deviceId: '',
      batchNo: '',
      reqChannel: '',
      uid: '',
      enterTime: '',
      leaveTime: '',
      formData: {
        gender: "",
        birthday: "",
        education: "",
        educationMajor: "",
        contactAddress: "",
        maritalStatus: "",
        childrenNum: "",
        lastCompanyName: "",
        lastJobPosition: "",
        school: "",
      },
      languageStatus: '', // 后台返回的key
      key: {}, // 用于提交后台的值
      //把每个表单中的验证取名的testName 组成一个数组
      testName: [
        { name: "gender", index: "1" },
        { name: "education", index: "3" },
        { name: "school", index: "4" },
        { name: "educationMajor", index: "5" },
        { name: "contactAddress", index: "6" },
        { name: "maritalStatus", index: "7" },
        { name: "childrenNum", index: "8" },
        { name: "lastCompanyName", index: "9" },
        { name: "lastJobPosition", index: "10" },
      ],

      /*-------------------------------------------供选择的枚举值 start-----------------------------------------------------*/
      genderList: [
        { name: this.$t('personInfo.male'), value: "M" },
        { name: this.$t('personInfo.female'), value: "F" },
      ],
      martialStatusArray: [
        // Single/Married/Seperated/Divorced/Widowed
        { value: "Single", name: this.$t('personInfo.single') },
        { value: "Married", name: this.$t('personInfo.married') },
        { value: "Seperated", name: this.$t('personInfo.seperated') },
        { value: "Divorced", name: this.$t('personInfo.divorced') },
        { value: "Widowed", name: this.$t('personInfo.widowed') },
      ],
      fromBackEnd: false, //由后台带出标志位

      productSource: '',
      stepOneVisible: false,
      creditPopVisible: false,
      custId: '',
      applyNo: '',
      crdStage: '',
      bubbleTip: '',
      fromRouteName: '',
      routeName: '',
      nativePageName: 'com.xloan.cash.ui.applycredit.CreditStep1Activity', // 原生页面名称, 默认紧急联系人前置的个人资料页面
      packageName: '',
      birthdayView: '',
      isTriggerPush: false,
      // 缓存用户填写的个人信息
      currentDate: new Date(),
      educationList: [
        {
          name: this.$t('personInfo.educationLevel.secondary'),
          value: "Secondary"
        },
        {
          name: this.$t('personInfo.educationLevel.associate'),
          value: "Associate"
        },
        {
          name: this.$t('personInfo.educationLevel.bachelor'),
          value: "Bachelor"
        },
        {
          name: this.$t('personInfo.educationLevel.master'),
          value: "Master"
        },
        {
          name: this.$t('personInfo.educationLevel.others'),
          value: "Others"
        }
      ],
    };
  },
  computed: {
    ...mapState(['userInfo']),
  },
  async created() {
    this.routeName = this.$route.name
    this.productSource = localStorage.getItem('productSource')
    this.setInitData()
  },
  mounted() {
    this.cacheJumpPage()
  },
  methods: {
    ...mapActions(['fetchInviteInfo', 'updateForm']),
    cacheJumpPage() {
      cacheJumpPage(this.routeName)
    },
    backFun() {
      this.$router.push('/home');
    },
    /**
     * 处理缓存数据
     * 没有数据则缓存，有则赋值
     */
    async setInitData() {
      if (this.userInfo) {
        const keys = Object.keys(this.formData);
        keys.forEach(key => {
          const value = this.userInfo[key]?.toString();
          if (value) {
            if (key === 'birthday') {
              const date = new Date(value);
              this.birthdayChange({ value: date });
              this.currentDate = date;
            } else if (key === 'education') {
              // 兼容旧数据，不是educationList的某一个值，默认Others
              const education = this.educationList.find(item => item.value === value)?.value || 'Others';
              this.formData[key] = education;
            } else {
              this.formData[key] = value;
            }
          }
        });
      }
    },
    //全局焦点事件捕获
    focusCapture() {
      if (this.fromBackEnd) {
        this.fromBackEnd = false
      }
    },
    //全局点击事件捕获
    clickCapture() {
      if (this.fromBackEnd) {
        this.fromBackEnd = false
      }
    },
    genderChange(e) {
      this.formData.gender = e.value.value;
    },
    //Martial status
    marticalChange(e) {
      this.formData.maritalStatus = e.value.value;
    },
    handleInputChildren(value) {
      let val = value && value.trim().replace(/^0+/, '');
      // Ensure the val is a non-negative integer
      if (!/^\d+$/.test(val) && val !== '') {
        val = '';
      }
      if (val === '') {
        val = '0';
      }
      this.formData.childrenNum = val;
    },
    //前往下一步
    handleGoNext() {
      const _this = this;
      _this.$refs.form.validateWithInfo().then(({ isValid, errors }) => {
        console.log('🎉 ~ file: personInfo.vue ~ line: 471 ~ errors: ', errors);
        //如果表单验证通过
        if (isValid) {
          this.submit()
        } else {
          // 找到第一个错误的字段
          const firstError = Object.keys(errors).filter(key => errors[key].length)[0];
          const fieldIdIndexMap = {
            [this.$t('personInfo.genderLower')]: '1',
            [this.$t('personInfo.birthLower')]: '2',
            [this.$t('personInfo.educationalLower')]: '3',
            [this.$t('personInfo.schoolLower')]: '4',
            [this.$t('personInfo.majorLower')]: '5',
            [this.$t('personInfo.addressLower')]: '6',
            [this.$t('personInfo.maritalStatusLower')]: '7',
            [this.$t('personInfo.childrenNumLower')]: '8',
            [this.$t('personInfo.lastCompanyNameLower')]: '9',
            [this.$t('personInfo.lastJobPositionLower')]: '10'
          };

          const elementId = fieldIdIndexMap[firstError];
          if (elementId) {
            // 根据错误字段找到对应的 DOM 元素 ID
            jumpIndex(elementId);
          }
        }
      })
        .catch((e) => {
          console.log(e);
        });
    },
    async submit() {
      try {
        await this.updateForm(this.formData);
        this.isTriggerPush = true;
        this.$router.push('/identityInfo');
      } catch (err) {
        console.log('🎉 ~ file: personInfo.vue ~ line: 511 ~ err: ', err);
      }
    },
    birthdayChange(data) {
      const formatValue = dateFormatDateMonthYear(data.value);
      this.formData.birthday = dateFormatYearMonthDate(data.value);
      this.birthdayView = formatValue;
    },
    educationChange(e) {
      this.formData.education = e.value.value;
    }
  },
};
</script>

<style scoped lang="scss">
.personinfo-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  background: #EFF3F6;
}
.credit-index {
  flex: 1;
  padding-top: 24px;
  box-sizing: border-box;
  background: #fff;
  border-top-left-radius: 36px;
  border-top-right-radius: 36px;
  .personForm {
    height: 100%;
    .c-button {
      @include themify() {
        background: themed("color");
      }
      box-shadow: none;
      margin-top: 35px;
    }
  }

  .formBox {
    //height: calc(100% - 280px);
    overflow-y: auto;
    box-sizing: border-box;
    &>div{
      margin-left: 24px;
      margin-right: 24px;
    }
  }
  .fill_item {
    margin-bottom: 18px;
    text-align: left;
    position: relative;
  }
  .select_one {
    margin-top: 0;
  }
  .select_wrap {
    position: relative;
  }
  /*输入错误提示*/
  .fill_item .fill_phone_input {
    box-sizing: border-box;
    padding-left: 64px;
    width: 288px;
    height: 34px;
    background: #ffffff;
    border: 2px solid #e3e5e9;
    border-radius: 8px;
    color: #1b3155;
    font-size: 14px;
  }
  .fill_item .fill_phone_input:focus {
    border: 2px solid #00C65D;
  }

  /*输入框的样式修改*/
  /*设置输入框提示语的样式*/
  .fill_item input::placeholder {
    font-size: 32px;
    color: #919db3;
  }
  .fill_item textarea::placeholder {
    font-size: 32px;
    color: #919db3;
  }
  /* -----------------下拉选择菜单样式 --------------------*/
  .sticky-footer-container {
    background: #ffffff;
  }
}
</style>
