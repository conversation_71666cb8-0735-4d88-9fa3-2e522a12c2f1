<template>
  <person-info v-if="hasAllPermissions" />
  <PermissionDialog v-else v-model="dialogVisible" @authorize="requestPermissions" @return="handleReturn"/>
</template>

<script>
import PersonInfo from "./personInfo.vue";
import PermissionDialog from "./permissionDialog.vue";
import { checkPermissions, disableNativeBack, enableNativeBack, requestPermissions } from "@/api/native";
import { PERMISSION_ENUMS } from "@/enums";
export default {
  name: "PersonInfoIndex",
  components: {
    PersonInfo,
    PermissionDialog
  },
  data() {
    return {
      hasAllPermissions: false,
      dialogVisible: false,
    }
  },
  created() {
    // 统一进行权限检查，不再区分iOS和Android
    this.checkPermissionss();
  },
  mounted() {
    disableNativeBack(() => {
      this.$router.push('/home');
    });
  },
  beforeDestroy() {
    enableNativeBack();
  },
  methods: {
    async checkPermissionss() {
      try {
        const res = await checkPermissions({
          permissions: Object.values(PERMISSION_ENUMS)
        })
        if (res.deny === '[]' || (Array.isArray(res.deny) && res.deny.length === 0)) {
          this.hasAllPermissions = true;
          this.dialogVisible = false;
        } else {
          this.hasAllPermissions = false;
          this.dialogVisible = true;
        }
      } catch (err) {
        this.hasAllPermissions = true;
      }
    },
    async requestPermissions() {
      try {
        const res = await requestPermissions({
          permissions: Object.values(PERMISSION_ENUMS)
        }) || {}
        if (res.deny === '[]' || (Array.isArray(res.deny) && res.deny.length === 0)) {
          this.hasAllPermissions = true;
        } else {
          this.hasAllPermissions = false;
          this.dialogVisible = true;
        }
      } catch (err) {
        this.hasAllPermissions = false;
        this.$toast(this.$t('toastMsg.permissionRequestFailed'));
      }
    },
    handleReturn() {
      this.$router.push('/home');
    }
  },
  filters: {},
  watch: {}
};
</script>

<style lang="less" scoped>

</style>
