<template>
  <van-dialog className="permission-dialog-container" v-model="internalShow" :show-cancel-button="false" :show-confirm-button="false">
    <div class="flex-col section_2">
      <div class="flex-col self-stretch group">
        <div class="flex-col justify-start items-center self-stretch text-wrapper">
          <span class="text">{{ $t('personInfo.authTitle') }}</span>
        </div>
        <div class="flex-col justify-start items-center self-center relative group_2">
          <img
            class="shrink-0 image"
            src="@/assets/images/login/logo.png"
          />
        </div>
      </div>
      <div class="flex-col self-center group_3">
        <span class="self-center font text_2">{{ $t('personInfo.welcome', {appName: 'HR APP'}) }}</span>
        <span class="self-stretch font text_3 mt-25">{{ $t('personInfo.obtainPermission') }}</span>
      </div>
      <div class="flex-col self-stretch group_4">
        <CButton :name="$t('personInfo.startAuth')" @click="handleAuthorize"/>
        <div class="flex-col justify-start items-center text-wrapper_3 mt-16" @click="handleReturn"><span class="text_5">{{ $t('personInfo.returnBtn') }}</span></div>
      </div>
    </div>
  </van-dialog>
</template>

<script>
import { Dialog } from 'vant';
import CButton from "@/components/c-button.vue";

export default {
  name: 'PermissionDialog',
  components: {
    CButton,
    [Dialog.Component.name]: Dialog.Component,
  },
  props: {
    // v-model 需要的 value 属性
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 内部的 show 状态用于绑定到 van-dialog 的 v-model
      internalShow: this.value,
    };
  },
  methods: {
    handleAuthorize() {
      this.$emit('authorize');
    },
    handleReturn() {
      this.$emit('return');
    }
  },
  watch: {
    // 监听 value 的变化，保持同步
    value(newVal) {
      this.internalShow = newVal;
    },
    // 当 internalShow 变化时，更新父组件的 v-model
    internalShow(newVal) {
      this.$emit('update:value', newVal);
    },
  },
};
</script>

<style scoped lang="scss">
.permission-dialog-container {
  width: 560px;
  :deep(.c-button) {
    height: 88px;
  }
}
.mt-25 {
  margin-top: 25px;
}
.section_2 {
  padding-bottom: 22px;
  background-color: #ffffff;
  border-radius: 16px;
  .group {
    height: 376px;
    .text-wrapper {
      padding: 58px 0 234.5px;
      background-image: linear-gradient(180deg, #c7e0ff 0%, #e8f2ff66 62.4%, #ffffff00 84.2%);
      border-radius: 16px 16px 0px 0px;
      width: 100%;
      .text {
        color: #1b3155;
        font-size: 36px;
        font-weight: 700;
        line-height: 34.5px;
      }
    }
    .group_2 {
      margin-top: -201px;
      padding: 8px 0 20px;
      overflow: hidden;
      width: 250px;
      .image {
        width: 241px;
        height: 222px;
      }
      .image_17 {
        width: 66.5px;
        height: 89px;
      }
      .pos_16 {
        position: absolute;
        left: 15px;
        bottom: 0;
      }
      .image_9 {
        width: 71.5px;
        height: 97.5px;
      }
      .pos_8 {
        position: absolute;
        left: 14px;
        bottom: 58.5px;
      }
      .image_3 {
        width: 53px;
        height: 75px;
      }
      .pos_2 {
        position: absolute;
        left: 28px;
        top: 41.5px;
      }
      .image_16 {
        width: 58.5px;
        height: 98px;
      }
      .pos_15 {
        position: absolute;
        right: 26px;
        bottom: 0;
      }
      .image_13 {
        width: 75px;
        height: 88px;
      }
      .pos_12 {
        position: absolute;
        right: 14.5px;
        bottom: 67.5px;
      }
      .image_4 {
        width: 43px;
        height: 58px;
      }
      .pos_3 {
        position: absolute;
        right: 36.5px;
        top: 40.5px;
      }
      .image_14 {
        width: 61.5px;
        height: 103px;
      }
      .pos_13 {
        position: absolute;
        left: 88px;
        bottom: 0;
      }
      .image_7 {
        width: 53px;
        height: 43px;
      }
      .pos_6 {
        position: absolute;
        left: 50%;
        top: 72.5px;
        transform: translateX(-50%);
      }
      .image_5 {
        width: 35px;
        height: 43px;
      }
      .pos_4 {
        position: absolute;
        left: 50%;
        top: 46.5px;
        transform: translateX(-50%);
      }
      .image_2 {
        width: 40px;
        height: 36px;
      }
      .pos {
        position: absolute;
        left: 50%;
        top: 38px;
        transform: translateX(-50%);
      }
      .image_6 {
        width: 31px;
        height: 22px;
      }
      .pos_5 {
        position: absolute;
        left: 50%;
        top: 58px;
        transform: translateX(-50%);
      }
      .image_11 {
        width: 60px;
        height: 76px;
      }
      .pos_10 {
        position: absolute;
        left: 50%;
        bottom: 81px;
        transform: translateX(-50%);
      }
      .image_8 {
        width: 27px;
        height: 17px;
      }
      .pos_7 {
        position: absolute;
        left: 50%;
        top: 88.5px;
        transform: translateX(-50%);
      }
      .image_12 {
        width: 43px;
        height: 75px;
      }
      .pos_11 {
        position: absolute;
        right: 72px;
        bottom: 81.5px;
      }
      .image_15 {
        width: 21px;
        height: 16px;
      }
      .pos_14 {
        position: absolute;
        left: 88px;
        bottom: 86.5px;
      }
      .image_10 {
        width: 44px;
        height: 66px;
      }
      .pos_9 {
        position: absolute;
        left: 69px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  .group_3 {
    margin-top: 40px;
    width: 443px;
    .font {
      font-size: 24px;
    }
    .text_2 {
      color: #1b3155;
      line-height: 23px;
    }
    .text_3 {
      color: #7b8da8;
      line-height: 36px;
    }
  }
  .group_4 {
    margin-top: 58px;
    padding: 0 24px;
    .text-wrapper_3 {
      padding: 32.5px 0 32.5px;
      background-color: #d7d7d700;
      width: 512px;
      .text_5 {
        color: #536786;
        font-size: 32px;
        line-height: 23px;
      }
    }
  }
}
</style>
