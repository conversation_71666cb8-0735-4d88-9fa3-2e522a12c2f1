<template>
  <div class="identityinfo-container">
    <public-header  :pageName="$t('title.identityDetails')" :backFun="backFun" :fromRouteName="fromRouteName" headerColor="#EFF3F6">
    </public-header>
    <form-page-header/>
    <div class="credit-index">
      <ValidationObserver slim ref="form">
        <form
          class="identityForm"
          @focus.capture="focusCapture"
          @click.capture="clickCapture"
          novalidate
        >
          <!--信息填写 1 start-->
          <div class="formBox">

            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_1"
              :name="$t('identityInfo.firstNameLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  id="firstNameBox"
                  :is-required="true"
                  v-model="formData.firstName"
                  :label="$t('identityInfo.firstNameUpper')"
                  maxlength="30"
                />
              </div>
            </ValidationProvider>

            <!-- 姓氏 -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_3"
              :name="$t('identityInfo.lastNameLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  :is-required="true"
                  :id="'lastNameBox'"
                  v-model="formData.lastName"
                  :label="$t('identityInfo.lastNameUpper')"
                  maxlength="30"
                />
              </div>
            </ValidationProvider>

            <!-- ID Type -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_4"
              :name="$t('identityInfo.idTypeLower')"
              rules="requiredSelect|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="select_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <DropDownList
                  :dataList="idTypeList"
                  :label="$t('identityInfo.idTypeUpper')"
                  @change="idTypeChange($event)"
                  v-model="formData.idType"
                  :isRequired="true"
                ></DropDownList>
              </div>
            </ValidationProvider>

            <!-- ID Number -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_5"
              :name="$t('identityInfo.idNumberLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  id="idNumberBox"
                  :is-required="true"
                  v-model="formData.idNumber"
                  :label="$t('identityInfo.idNumberUpper')"
                  maxlength="100"
                />
              </div>
            </ValidationProvider>

            <!-- Add new hasIdCard field for Tanzania -->
            <ValidationProvider
              v-if="isTanzania"
              tag="div"
              class="fill_item clearfix"
              id="d_jump_6"
              :name="$t('identityInfo.hasIdCardLower')"
              rules="requiredSelect"
              v-slot="{ failed, errors }"
            >
              <div
                class="select_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <DropDownList
                  :dataList="hasIdCardOptions"
                  :label="$t('identityInfo.hasIdCardUpper')"
                  @change="hasIdCardChange"
                  v-model="formData.hasIdCard"
                  :isRequired="true"
                />
              </div>
            </ValidationProvider>

            <!-- Modify front photo validation -->
            <ValidationProvider
              class="fill_item clearfix"
              id="d_jump_7"
              :name="$t('identityInfo.idCardFrontPhotoLower')"
              :rules="frontPhotoRules"
              v-slot="{ failed, errors }"
            >
              <div
                class="select_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <id-card-uploader
                  :is-required="isFrontPhotoRequired"
                  :title="$t('identityInfo.idCardFrontPhotoUpper')"
                  v-model="formData.idCardFrontPhoto"
                  :preview-url="previewUrls.idCardFrontPhotoUrl"
                  :bg-img="uploaderBg1"
                  @complete="(file) => handleComplete('idCardFrontPhoto', file)"
                  @close="handleClose('idCardFrontPhoto')"
                />
              </div>
            </ValidationProvider>

            <ValidationProvider
              class="fill_item clearfix"
              id="d_jump_8"
              :name="$t('identityInfo.idCardBackPhotoLower')"
              v-slot="{ failed, errors }"
            >
              <div
                class="select_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <id-card-uploader :is-required="false" :title="$t('identityInfo.idCardBackPhotoUpper')" v-model="formData.idCardBackPhoto" :preview-url="previewUrls.idCardBackPhotoUrl" :bg-img="uploaderBg2" @complete="(file) => handleComplete('idCardBackPhoto', file)" @close="handleClose('idCardBackPhoto')"/>
              </div>
            </ValidationProvider>

          </div>
          <sticky-footer-container>
            <template #content>
              <footer-note/>
              <CButton :name="$t('common.Next')" v-debounce="handleGoNext"/>
            </template>
          </sticky-footer-container>
        </form>
      </ValidationObserver>
    </div>
  </div>
</template>

<script>
import formPageHeader from '@/components/form-page-header.vue'
import DropDownList from "@/components/dropDownList.vue";
import PalmInput from "@/components/palm-input.vue";
import StickyFooterContainer from '@/components/sticky-footer-container.vue'
import CButton from "@/components/c-button.vue";
import idCardUploader from "../components/id-card-uploader.vue";
import uploaderBg1 from "@/assets/images/entryProcess/identityInfo/upload-bg1.png";
import uploaderBg2 from "@/assets/images/entryProcess/identityInfo/upload-bg2.png";
import { jumpIndex } from "@/utils/anchorPoint.js";
import { ValidationObserver, ValidationProvider } from "vee-validate";
import { cacheJumpPage, isIOS } from '@/utils/tools.js'
import FooterNote from "@/components/FooterNote.vue";
import { mapActions, mapState } from "vuex";
import { compressImage } from "@/utils/image";
import { fetchUserIdType } from "@/axios/api";

let tmpFromPage = "";
export default {
  name: "identityInfo",
  components: {
    FooterNote,
    formPageHeader,
    DropDownList,
    PalmInput,
    ValidationProvider,
    ValidationObserver,
    CButton,
    StickyFooterContainer,
    idCardUploader
  },
  data() {
    return {
      phone: '',
      deviceId: '',
      batchNo: '',
      reqChannel: '',
      uid: '',
      enterTime: '',
      leaveTime: '',
      formData: {
        firstName: '',
        lastName: '',
        idType: '',
        idNumber: '',
        idCardFrontPhoto: '',
        idCardBackPhoto: '',
        hasIdCard: '1',
      },
      previewUrls: {
        idCardFrontPhotoUrl: '',
        idCardBackPhotoUrl: '',
      },
      uploaderBg1,
      uploaderBg2,
      key: {}, // 用于提交后台的值
      fromRouteName: '',
      routeName: '',
      idTypeListMap: {},
      // 缓存用户填写的个人信息
      hasIdCardOptions: [
        { name: this.$t('identityInfo.yes'), value: '1' },
        { name: this.$t('identityInfo.no'), value: '0' },
      ],
    };
  },
  computed: {
    ...mapState(['userInfo']),
    idTypeList() {
      return this.idTypeListMap[this.userInfo?.country] || []
    },
    isTanzania() {
      return this.userInfo?.country === 'TZ'
    },
    isFrontPhotoRequired() {
      return !this.isTanzania || this.formData.hasIdCard === '1'
    },
    frontPhotoRules() {
      return this.isFrontPhotoRequired ? 'requiredSelect' : ''
    }
  },
  beforeRouteEnter(to, from, next) {
    tmpFromPage = from.name
    next()
  },
  async created() {
    /*if (!this.userInfo?.id) {
      this.$router.replace('/personInfo')
      return;
    }*/
    this.routeName = this.$route.name
    this.setInitData()
    this.fetchUserIdType();
    console.log("identityInfo----fromRouteName", tmpFromPage);
    this.fromRouteName = tmpFromPage;
    this.$store.commit("SET_FROMROUTENAME", tmpFromPage);
  },
  mounted() {
    this.cacheJumpPage()
  },
  methods: {
    ...mapActions(['uploadFile', 'updateForm']),
    cacheJumpPage() {
      cacheJumpPage(this.routeName)
    },
    backFun() {
    },
    /**
     * 处理缓存数据
     * 没有数据则缓存，有则赋值
     */
    setInitData() {
      if (this.userInfo) {
        const keys = Object.keys(this.formData);
        keys.forEach((key) => {
          const value = this.userInfo[key]?.toString();
          if (value) {
            this.formData[key] = value;
          }
        });
        const previewUrls = ['idCardFrontPhotoUrl', 'idCardBackPhotoUrl']
        previewUrls.forEach((key) => {
          const value = this.userInfo[key]?.toString();
          if (value) {
            this.previewUrls[key] = value;
          }
        });
        // Set default hasIdCard value if not present
        if (!this.formData.hasIdCard) {
          this.formData.hasIdCard = '1';
        }
      }
    },
    async fetchUserIdType() {
      try {
        const data = await fetchUserIdType()
        const map = {};
        Object.keys(data).forEach(key => {
          map[key] = data[key].map(item => ({
            name: item,
            value: item,
          }))
        })
        this.idTypeListMap = map;
      } catch (err) {
        console.log('err', err)
      }
    },
    //全局焦点事件捕获
    focusCapture() {
      if (this.fromBackEnd) {
        this.fromBackEnd = false
      }
    },
    //全局点击事件捕获
    clickCapture() {
      if (this.fromBackEnd) {
        this.fromBackEnd = false
      }
    },
    idTypeChange(e) {
      this.formData.idType = e.value.value;
      this.key.idType = e.value.key;
    },
    hasIdCardChange(e) {
      this.formData.hasIdCard = e.value.value
    },
    async handleComplete(key, { file }) {
      try {
        // File对象
        // 压缩前文件大小
        console.log('Original file size:', file.size);
        const compressedFile = isIOS ? await compressImage(file) : file;
        // 打印文件大小
        console.log('Compressed file size:', compressedFile.size);
        const data = await this.uploadFile(compressedFile);
        this.formData[key] = data.relativeUrl || '';
        this.previewUrls[`${key}Url`] = data.absoluteUrl || '';
      } catch (err) {
        console.log('🎉 ~ file: identityInfo.vue ~ line: 460 ~ err: ', err);
      }
    },
    handleClose(key) {
      this.formData[key] = '';
      this.previewUrls[`${key}Url`] = '';
    },
    //前往下一步
    handleGoNext() {
      const _this = this;
      _this.$refs.form.validateWithInfo().then(({ isValid, errors }) => {
        //如果表单验证通过
        if (isValid) {
          this.submit()
        } else {
          // 找到第一个错误的字段
          const firstError = Object.keys(errors).filter(key => errors[key].length)[0];
          const fieldIdIndexMap = {
            [this.$t('identityInfo.firstNameLower')]: '1',
            [this.$t('identityInfo.lastNameLower')]: '3',
            [this.$t('identityInfo.idTypeLower')]: '4',
            [this.$t('identityInfo.idNumberLower')]: '5',
            [this.$t('identityInfo.hasIdCardLower')]: '6',
            [this.$t('identityInfo.idCardFrontPhotoLower')]: '7',
            [this.$t('identityInfo.idCardBackPhotoLower')]: '8'
          };

          const elementId = fieldIdIndexMap[firstError];
          if (elementId) {
            // 根据错误字段找到对应的 DOM 元素 ID
            jumpIndex(elementId);
          }
        }
      })
      .catch((e) => {
        console.log(e);
      });
    },
    async submit() {
      try {
        await this.updateForm(this.formData);
        this.$store.commit('SET_FORM_DATA', this.previewUrls);
        this.$router.push('/faceVerification');
      } catch (err) {
        console.log('🎉 ~ file: personInfo.vue ~ line: 540 ~ err: ', err);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.identityinfo-container {
  height: 100%;
  box-sizing: border-box;
  background: #EFF3F6;
}

.credit-index {
  height: 100%;
  padding-top: 24px;
  box-sizing: border-box;
  background: #fff;
  border-top-left-radius: 36px;
  border-top-right-radius: 36px;

  .identityForm {
    height: 100%;

    .c-button {
      @include themify() {
        background: themed("color");
      }
      box-shadow: none;
      margin-top: 35px;
    }
  }

  .formBox {
    //height: calc(100% - 280px);
    overflow-y: auto;
    padding-bottom: 45px;
    box-sizing: border-box;

    & > div {
      margin-left: 24px;
      margin-right: 24px;
    }
  }

  .fill_item {
    margin-bottom: 18px;
    text-align: left;
    position: relative;
  }

  .select_one {
    margin-top: 0;
  }

  .select_wrap {
    position: relative;
  }

  /*输入框的样式修改*/
  /*设置输入框提示语的样式*/
  .fill_item input::placeholder {
    font-size: 32px;
    color: #919db3;
  }

  .fill_item textarea::placeholder {
    font-size: 32px;
    color: #919db3;
  }

  /* -----------------下拉选择菜单样式 --------------------*/
  .sticky-footer-container {
    background: #ffffff;
  }
}
</style>
