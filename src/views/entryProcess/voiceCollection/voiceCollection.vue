<template>
  <div class="voice-collection-container flex-col page">
    <public-header :pageName="$t('title.voiceCollection')" :fromRouteName="fromRouteName" />
    <div class="flex-col items-center group_3">
      <CircleButton ref="circle" :has-permission="hasPermission" @start="onStart" @completed="onCompleted" @end="onEnd"
        class="mt-42" />
      <div class="record-btn-wrapper">
        <CButton v-debounce="handleRecordClick" :name="recordBtnText" :disabled="!hasPermission" class="record-btn"
          :data-status="recordStatus" />
      </div>
      <div class="notice-text-list">
        <p class="font_2 text_4">
          {{ noticeTextList[0] }}
        </p>
      </div>
      <div class="flex-col items-start section_2">
        <span class="font_3">{{ $t('voiceCollection.guarantee') }}</span>
      </div>
      <div class="notice-text-list mt-42">
        <p class="font_2 text_4">
          {{ noticeTextList[1] }}
        </p>
      </div>
    </div>
    <sticky-footer-container>
      <template #content>
        <CButton :name="$t('common.Next')" :disabled="disabled" v-debounce="handleNextClick" />
      </template>
    </sticky-footer-container>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import StickyFooterContainer from '@/components/sticky-footer-container.vue'
import CButton from "@/components/c-button.vue";
import CircleButton from "./components/CircleButton.vue";
import { checkPermissions, getCallbackName, requestPermissions, startRecord, stopRecord } from "@/api/native";
import { base64ToBlob } from "@/utils/file";
import { PERMISSION_ENUMS } from "@/enums";



let tmpFromPage = "";

export default {
  name: 'VoiceCollection',
  components: {
    StickyFooterContainer,
    CButton,
    CircleButton
  },
  beforeRouteEnter(to, from, next) {
    tmpFromPage = from.name
    next()
  },
  data() {
    return {
      fromRouteName: '',
      hasPermission: false,
      isCompleted: false,
      isCancelled: false,
      disabled: true,
      noticeTextList: [
        this.$t('voiceCollection.noticeTextList.0'),
        this.$t('voiceCollection.noticeTextList.1'),
      ],
      voicePrint: '',
      startTime: 0,
      recording: false,
      recordStatus: 'initial',
    }
  },
  computed: {
    recordBtnText() {
      const textMap = {
        initial: this.$t('voiceCollection.recordBtn.start'),
        recording: this.$t('voiceCollection.recordBtn.end'),
        completed: this.$t('voiceCollection.recordBtn.reRecord')
      };
      return textMap[this.recordStatus];
    }
  },
  created() {
    this.fromRouteName = tmpFromPage;
    this.$store.commit("SET_FROMROUTENAME", tmpFromPage);

    // 统一使用客户端权限检查，不再区分iOS和Android
    this.clientCheckPermission();
  },
  methods: {
    ...mapActions(['uploadFile', 'updateForm']),
    clientCheckPermission() {
      checkPermissions({
        permissions: [PERMISSION_ENUMS.RECORD_AUDIO]
      }).then((res) => {
        if (res.deny === '[]' || (Array.isArray(res.deny) && res.deny.length === 0)) {
          this.hasPermission = true;
        } else {
          this.hasPermission = false;
        }
      }).catch((err) => {
        console.log('🎉 ~ file: voiceCollection.vue ~ line: 111 ~ err: ', err);
        this.hasPermission = false;
      })
    },
    clientRequestPermission() {
      requestPermissions({
        permissions: [PERMISSION_ENUMS.RECORD_AUDIO]
      }).then(res => {
        if (res.deny === '[]' || (Array.isArray(res.deny) && res.deny.length === 0)) {
          this.hasPermission = true;
        }
      }).catch((err) => {
        console.log('🎉 ~ file: voiceCollection.vue ~ line: 123 ~ err: ', err);
      })
    },
    handleAuth() {
      setTimeout(() => {
        this.clientRequestPermission();
      }, 1000);
    },
    handleRecordClick() {
      // 统一使用客户端权限检查，不再区分iOS和Android
      if (!this.hasPermission) {
        this.handleAuth();
        return;
      }

      if (this.recordStatus === 'initial' || this.recordStatus === 'completed') {
        this.recordStatus = 'recording';
        this.$refs.circle.handleClick();
      } else if (this.recordStatus === 'recording') {
        this.recordStatus = 'completed';
        this.$refs.circle.handleClick();
      }
    },

    async onStart() {
      console.log('onStart 开始');
      this.recordStatus = 'recording';
      this.disabled = true;
      this.isCompleted = false;
      this.isCancelled = false;
      this.startTime = Date.now();

      // 统一使用客户端录音方式
      const stopCallback = getCallbackName('stopRecord');
      startRecord({
        duration_min: 5000,
        duration_max: 10000,
        stop_callback: stopCallback,
      })
        .then(res => {
          if (this.isCancelled) return;
          if (res.status === 0) {
            this.recording = true;
          }
          if (res.status === 2) {
            this.$toast(res.msg)
          }
          if (res.status === 3) {
            requestPermissions({
              permissions: [PERMISSION_ENUMS.RECORD_AUDIO]
            })
          }
        })
        .catch(err => {
          console.log("🎉 ~ file: voiceCollection.vue ~ line: 25 ~ err: ", err);
        })
      window[stopCallback] = (res) => {
        console.log('🎉 ~ file: voiceCollection.vue ~ line: 217 ~ res: ', res);
      }
    },
    async onCompleted() {
      if (!this.recording) return;
      this.isCompleted = true;
      this.recordStatus = 'completed';
      this.stopRecord();
    },
    async stopRecord() {
      console.log('停止录音');
      // 统一使用客户端停止录音方式
      try {
        const res = await stopRecord();
        this.handleRecordResponse(res);
      } catch (err) {
        console.log('🎉 ~ file: voiceCollection.vue ~ line: 255 ~ err: ', err);
        this.disabled = true;
        this.$refs.circle.setPercent(0);
        this.recordStatus = 'initial';
      }
    },
    async onEnd() {
      console.log("触摸结束，触发事件！");
      const endTime = Date.now();
      const duration = endTime - this.startTime;

      if (duration < 5000) {
        this.isCancelled = true;
        this.recordStatus = 'initial';
        this.$toast(this.$t('toastMsg.recordingLess'));
        this.$refs.circle.setPercent(0);
        // 统一使用客户端停止录音
        stopRecord();
        return;
      }

      if (!this.recording) return;
      this.recordStatus = 'completed';
      this.stopRecord();
    },
    async handleRecordResponse(res) {
      try {
        if (res.status === 0) {
          const file = res.file;
          let blob;
          if (file?.blob instanceof Blob) {
            // 如果已经是Blob对象,直接使用
            blob = file.blob;
          } else if (file.base64) {
            // 如果是base64格式,需要转换
            blob = base64ToBlob(file.base64, file.fileType);
          } else {
            throw new Error(this.$t('voiceCollection.invalidFileFormat'));
          }

          const newFile = new File([blob], file.fileName || 'recording.aac', { type: blob.type });
          const data = await this.uploadFile(newFile) || {};
          if (data.absoluteUrl) {
            this.voicePrint = data.relativeUrl;
            this.disabled = false;
            this.$refs.circle.setPercent(100);
            this.recordStatus = 'completed';
          } else {
            this.disabled = true;
            this.$refs.circle.setPercent(0);
            this.recordStatus = 'initial';
          }
        }
      } catch (err) {
        console.error('处理录音文件失败:', err);
        this.disabled = true;
        this.$refs.circle.setPercent(0);
        this.recordStatus = 'initial';
      }
    },
    async handleNextClick() {
      if (this.disabled) return;
      if (this.voicePrint) {
        await this.updateForm({ voicePrint: this.voicePrint });
        this.$router.push('/contactInfo');
      }
    },
    beforeDestroy() {
      // 组件销毁时的清理工作已统一到客户端处理
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  background-color: #ffffff;
  height: 100%;

  .group_3 {
    flex: 1;
    margin-top: 27px;

    .notice-text-list {
      padding: 0 90px;
      text-align: center;
    }

    .font_2 {
      font-size: 28px;
      line-height: 42px;
      color: #919db3;
    }

    .section_2 {
      width: 480px;
      margin: 42px auto 0;
      padding: 12px 30px 7.5px;
      background-image: url('@/assets/images/entryProcess/voice-text-bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;

      .font_3 {
        font-size: 26px;
        line-height: 44px;
        color: #ff7a3a;
      }
    }
  }

  .record-btn-wrapper {
    margin-top: 38px;
    margin-bottom: 42px;

    .record-btn {
      padding: 26px 50px;
      border-radius: 60px;
      color: #FFF;
      text-align: center;
      font-size: 26px;
      font-weight: 500;
      box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.20);

      &[data-status="initial"] {
        background: linear-gradient(180deg, #4AB1FD 0%, #0074FF 100%);
      }

      &[data-status="recording"] {
        background: linear-gradient(180deg, #FFAF95 0%, #F00 100%);
      }

      &[data-status="completed"] {
        background: linear-gradient(180deg, #82EF3F 0%, #1AA32B 100%);
      }

      &:disabled {
        opacity: 0.5;
      }
    }
  }
}
</style>
