<template>
  <div
    class="circle-button"
  >
    <vue-awesome-progress
      :circle-width="10 * widthRatio"
      :line-width="10 * widthRatio"
      :duration="0.6"
      :show-text="false"
      :percentage="percent"
      :circle-radius="185 * widthRatio"
      circle-color="#ECF4FF"
      easing="0.17,0.67,0.83,0.67"
    />
    <div class="touch-area">
      <img src="@/assets/images/entryProcess/voiceCollection/microphone.png" alt="">
    </div>
    <img v-if="percent === 100" class="done" src="@/assets/images/entryProcess/voiceCollection/done.png" alt="">
  </div>
</template>

<script>
import VueAwesomeProgress from "vue-awesome-progress";
export default {
  components: {
    VueAwesomeProgress
  },
  props: {
    duration: {
      type: Number,
      default: 10000
    },
    frequency: {
      type: Number,
      default: 50
    },
  },
  data() {
    return {
      widthRatio: document.documentElement.clientWidth / 720,
      isAnimating: false,
      intervalId: null,
      percent: 0,
    };
  },
  computed: {
  },
  methods: {
    handleClick() {
      if (this.isAnimating) {
        clearInterval(this.intervalId); // 清除定时器
        this.isAnimating = false;
        this.$emit("end");
      } else {
        this.isAnimating = true;
        this.startInterval()
        this.$emit("start");
      }
    },
    setPercent(percent) {
      this.percent = percent;
    },
    startInterval() {
      this.setPercent(0); // 重置进度
      this.intervalId = setInterval(() => {
        if (this.percent < 100) {
          this.setPercent(this.percent + (this.frequency / this.duration) * 100);
        } else {
          // 如果圆环满了，触发事件并清除进度
          clearInterval(this.intervalId);
          this.isAnimating = false;
          this.$emit("completed");
        }
      }, this.frequency);
    },
  },
  watch: {
    /*hasPermission(newValue) {
      if (newValue) {
        this.startPress();
      }
    },*/
  },
  beforeDestroy() {
    clearInterval(this.intervalId); // 清除定时器
  },
};
</script>

<style scoped>
.circle-button {
  position: relative;
  width: 370px;
  height: 370px;
  background: #ECF4FF;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.touch-area {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 116.667px;
  height: 177.778px;
  img {
    max-height: 100%;
  }
}
.done {
  position: absolute;
  left: 50%;
  top: -20px;
  transform: translateX(-50%);
  width: 50px;
  height: 50px;
}
</style>
