<template>
  <div class="flex-col self-stretch group_9">
    <div class="flex-col group_10">
      <div class="flex-row justify-between items-center group_11">
        <div class="group_12">
          <span v-if="isRequired" class="font_7" style="margin-right: 6px">*</span>
          <span class="font_4 text_17">{{ title }}</span>
        </div>
      </div>
      <p class="format-tips">{{ $t('identityInfo.formatTips') }}</p>
      <div v-if="previewUrl" class="preview">
        <img :src="previewUrl" alt="" @click="handlePreview(previewUrl)">
        <span class="close" @click="closePreview">&times;</span>
      </div>
      <div v-else class="flex-col justify-start items-center section_1 view_2" :style="`background-image: url(${bgImg});`">
        <div class="upload-btn">
          <img src="@/assets/images/entryProcess/identityInfo/upload-btn.png" alt="">
        </div>
        <Uploader @complete="onComplete"/>
      </div>
    </div>
  </div>
</template>

<script>
import Uploader from "@/components/Uploader.vue";
import { ImagePreview } from "vant";

export default {
  components: {
    Uploader,
  },
  props: {
    isRequired: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: "",
    },
    bgImg: {
      type: String,
      default: "",
    },
    value: {
      type: String,
      default: null,
    },
    previewUrl: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  methods: {
    closePreview() {
      this.$emit("close");
    },
    handlePreview(value) {
      ImagePreview([value]);
    },
    onComplete(file) {
      this.$emit("change", file);
      this.$emit("complete", file);
    }
  },
};
</script>

<style scoped lang="scss">
.ml-23 {
  margin-left: 23px;
}
.mt-9 {
  margin-top: 9px;
}
.group_9 {
  margin-top: 21px;
  padding-top: 20px;
  border-top: solid 2px #dbe1ea;
  .group_10 {
    padding-left: 48px;
    padding-right: 48px;
    overflow: hidden;
    .group_11 {
      padding: 21.5px 0;
      .group_12 {
        width: 100%;
        line-height: 32px;
        .font_4 {
          font-size: 28px;
          font-weight: 700;
        }
        .text_17 {
          color: #1b3155;
        }
      }
    }
    .format-tips {
      color: #7b8da8;
      font-size: 24px;
      line-height: 23px;
      margin-bottom: 20px;
    }
    .section_1 {
      margin-left: 2.5px;
    }
    .preview {
      position: relative;
      height: 216px;
      img {
        object-fit: contain;
      }
      .close {
        position: absolute;
        top: 0;
        right: 0;
        width: 30px;
        font-size: 40px;
        color: #1b3155;
        z-index: 100;
      }
    }
    .view_2 {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      background-size: cover;
      background-repeat: no-repeat;
      height: 216px;
    }
    .van-uploader {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      opacity: 0;
    }
    .upload-btn {
      width: 180px;
      height: 180px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .font_7 {
      font-size: 28px;
      line-height: 12px;
      font-weight: 700;
      color: #ff0000;
    }
    .font_8 {
      font-size: 24px;
      line-height: 23px;
      color: #0074ff;
    }
  }
}
</style>
