<template>
  <div class="flex-row justify-between items-center group_3">
    <img
      class="image_7"
      src="../../../assets/images/entryProcess/people-icon.png"
    />
    <div class="group_4">
      <span class="font_3">{{current}}</span>
      <span class="font_4 text_6">/{{total}}</span>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    current: {
      type: Number,
      default: 0
    },
    total: {
      type: Number,
      default: 0
    },
  },
  data() {
    return {};
  },

  methods: {},
};
</script>

<style scoped lang="scss">
.group_3 {
  padding-left: 19px;
  border-left: solid 4px #0074ff;
  .image_7 {
    border-radius: 16px;
    width: 56px;
    height: 56px;
  }
  .group_4 {
    height: 42px;
    .font_3 {
      font-size: 56px;
      line-height: 40px;
      font-weight: 700;
      color: #0074ff;
    }
    .font_4 {
      font-size: 28px;
      line-height: 23px;
      font-weight: 700;
      color: #7b8da8;
    }
    .text_6 {
      line-height: 24px;
    }
  }
}
</style>
