<template>
  <div class="bankcard-info-container">
    <public-header :pageName="$t('title.contactDetails')" :fromRouteName="fromRouteName" headerColor="#EFF3F6">
    </public-header>
    <form-page-header/>
    <div class="credit-index">
      <ValidationObserver slim ref="form">
        <form
          class="contactForm"
          @focus.capture="focusCapture"
          @click.capture="clickCapture"
          novalidate
        >
          <!--信息填写 1 start-->
          <div class="formBox">

            <ContactNumber :current="1" :total="2"/>
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_1"
              :name="$t('contactInfo.emergencyContactRelationLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  :is-required="true"
                  :id="'emailBox'"
                  v-model="formData.emergencyContactRelation"
                  :label="$t('contactInfo.emergencyContactRelationUpper')"
                  maxlength="100"
                />
              </div>
            </ValidationProvider>

            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_2"
              :name="$t('contactInfo.emergencyContactNameLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  :is-required="true"
                  :id="'emergencyContactNameBox'"
                  v-model="formData.emergencyContactName"
                  :label="$t('contactInfo.emergencyContactNameUpper')"
                  maxlength="100"
                />
              </div>
            </ValidationProvider>

            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_3"
              :name="$t('contactInfo.emergencyContactPhoneLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  :is-required="true"
                  :id="'emergencyContactPhoneBox'"
                  v-model="formData.emergencyContactPhone"
                  :label="$t('contactInfo.emergencyContactPhoneUpper')"
                  maxlength="64"
                />
              </div>
            </ValidationProvider>
            <ContactNumber :current="2" :total="2"/>
<!--       guarantorContactRelation     -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_4"
              :name="$t('contactInfo.guarantorContactRelationLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  :is-required="true"
                  :id="'guarantorContactRelationBox'"
                  v-model="formData.guarantorContactRelation"
                  :label="$t('contactInfo.guarantorContactRelationUpper')"
                  maxlength="100"
                />
              </div>
            </ValidationProvider>

            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_5"
              :name="$t('contactInfo.guarantorContactNameLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  :is-required="true"
                  :id="'guarantorContactNameBox'"
                  v-model="formData.guarantorContactName"
                  :label="$t('contactInfo.guarantorContactNameUpper')"
                  maxlength="100"
                />
              </div>
            </ValidationProvider>
            <!--       guarantorContactPhone     -->
            <ValidationProvider
              tag="div"
              class="fill_item clearfix"
              id="d_jump_6"
              :name="$t('contactInfo.guarantorContactPhoneLower')"
              rules="required|min:1"
              v-slot="{ failed, errors }"
            >
              <div
                class="textarea_wrap"
                :class="{ error_input_border: failed }"
                :data-error="errors[0]"
              >
                <palm-input
                  :is-required="true"
                  :id="'guarantorContactPhoneBox'"
                  v-model="formData.guarantorContactPhone"
                  :label="$t('contactInfo.guarantorContactPhoneUpper')"
                  maxlength="64"
                />
              </div>
            </ValidationProvider>


          </div>
          <sticky-footer-container>
            <template #content>
              <footer-note/>
              <CButton :name="$t('common.Next')" v-debounce="handleGoNext"/>
            </template>
          </sticky-footer-container>
        </form>
      </ValidationObserver>
    </div>
  </div>
</template>

<script>
import formPageHeader from '@/components/form-page-header.vue'
import PalmInput from "@/components/palm-input.vue";
import StickyFooterContainer from '@/components/sticky-footer-container.vue'
import CButton from "@/components/c-button.vue";
import ContactNumber from "../components/contact-number.vue";
import { jumpIndex } from "@/utils/anchorPoint.js";
import { ValidationObserver, ValidationProvider } from "vee-validate";
import { cacheJumpPage } from '@/utils/tools.js'
import FooterNote from "@/components/FooterNote.vue";
import { mapActions, mapState } from "vuex";
import { virtualKeyboardMixin } from "@/utils/virtualKeyboard";

let tmpFromPage = "";
export default {
  name: "contactInfo",
  mixins: [virtualKeyboardMixin],
  components: {
    FooterNote,
    formPageHeader,
    PalmInput,
    ValidationProvider,
    ValidationObserver,
    CButton,
    StickyFooterContainer,
    ContactNumber,
  },
  data() {
    return {
      // 虚拟键盘处理配置
      virtualKeyboardOptions: {
        targetSelector: '.contactForm',
      },
      phone: '',
      deviceId: '',
      batchNo: '',
      reqChannel: '',
      uid: '',
      enterTime: '',
      leaveTime: '',
      formData: {
        emergencyContactRelation: '',
        emergencyContactName: '',
        emergencyContactPhone: '',
        guarantorContactRelation: '',
        guarantorContactName: '',
        guarantorContactPhone: '',
      },
      files: {
        file1: {},
        file2: {},
      },
      languageStatus: '', // 后台返回的key
      key: {}, // 用于提交后台的值
      emailError: false,
      //把每个表单中的验证取名的testName 组成一个数组
      testName: [
        { name: "gender", index: "1" },
        { name: "email", index: "2" },
        { name: "educationType", index: "3" },
        { name: "educationMajor", index: "4" },
        { name: "contactAddress", index: "6" },
        { name: "maritalStatus", index: "7" },
        { name: "numberOfChildren", index: "8" },
        { name: "lastCompanyName", index: "9" },
        { name: "lastJobPosition", index: "10" },
      ],

      /*-------------------------------------------供选择的枚举值 start-----------------------------------------------------*/
      fromBackEnd: false, //由后台带出标志位

      stepOneVisible: false,
      creditPopVisible: false,
      custId: '',
      applyNo: '',
      crdStage: '',
      bubbleTip: '',
      fromRouteName: '',
      routeName: '',
      nativePageName: 'com.xloan.cash.ui.applycredit.CreditStep1Activity', // 原生页面名称, 默认紧急联系人前置的个人资料页面
      packageName: '',
      birthdayView: '',
      // 缓存用户填写的个人信息
      currentDate: new Date()
    };
  },
  computed: {
    ...mapState(['userInfo']),
  },
  beforeRouteEnter(to, from, next) {
    tmpFromPage = from.name
    next()
  },
  async created() {
    this.routeName = this.$route.name
    this.setInitData()
    this.fromRouteName = tmpFromPage;
    this.$store.commit("SET_FROMROUTENAME", tmpFromPage);
  },
  mounted() {
    this.cacheJumpPage()
  },
  methods: {
    ...mapActions(['fetchInviteInfo', 'updateForm']),
    cacheJumpPage() {
      cacheJumpPage(this.routeName)
    },
    backFun() {
    },
    /**
     * 处理缓存数据
     * 没有数据则缓存，有则赋值
     */
    setInitData() {
      if (this.userInfo) {
        const keys = Object.keys(this.formData);
        keys.forEach((key) => {
          const value = this.userInfo[key]?.toString();
          if (value) {
            this.formData[key] = value;
          }
        });
      }
    },
    handleLocalData() {
      localStorage.setItem("uid", this.uid)
      const localData = Object.assign({}, { formData: this.formData }, { key: this.key }, { languageStatus: this.languageStatus })
      console.log('localData', localData)
      this.$store.commit('SET_contactInfoObj', {
        key: `contactInfoObj${this.uid}`,
        value: localData
      });
    },
    //全局焦点事件捕获
    focusCapture() {
      if (this.fromBackEnd) {
        this.fromBackEnd = false
      }
    },
    //全局点击事件捕获
    clickCapture() {
      if (this.fromBackEnd) {
        this.fromBackEnd = false
      }
    },
    //前往下一步
    handleGoNext() {
      const _this = this;
      _this.$refs.form.validateWithInfo().then(({ isValid, errors }) => {
        //如果表单验证通过
        if (isValid) {
          this.submit()
        } else {
          // 找到第一个错误的字段
          const firstError = Object.keys(errors).filter(key => errors[key].length)[0];
          const fieldIdIndexMap = {
            [this.$t('contactInfo.emergencyContactRelationLower')]: '1',
            [this.$t('contactInfo.emergencyContactNameLower')]: '2',
            [this.$t('contactInfo.emergencyContactPhoneLower')]: '3',
            [this.$t('contactInfo.guarantorContactRelationLower')]: '4',
            [this.$t('contactInfo.guarantorContactNameLower')]: '5',
            [this.$t('contactInfo.guarantorContactPhoneLower')]: '6'
          };

          const elementId = fieldIdIndexMap[firstError];
          if (elementId) {
            // 根据错误字段找到对应的 DOM 元素 ID
            jumpIndex(elementId);
          }
        }
      })
      .catch((e) => {
        console.log(e);
      });
    },
    async submit() {
      try {
        await this.updateForm(this.formData);
        this.$router.push('/bankCardInfo');
      } catch (err) {
        console.log('🎉 ~ file: contactInfo.vue ~ line: 389 ~ err: ', err);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.bankcard-info-container {
  height: 100%;
  box-sizing: border-box;
  background: #EFF3F6;
}

.credit-index {
  //height: 100%;
  padding-top: 38px;
  box-sizing: border-box;
  background: #fff;
  border-top-left-radius: 36px;
  border-top-right-radius: 36px;

  .contactForm {
    //height: 100%;

    .c-button {
      @include themify() {
        background: themed("color");
      }
      box-shadow: none;
      margin-top: 35px;
    }
  }

  .formBox {
    //height: calc(100% - 280px);
    overflow-y: auto;
    padding-bottom: 45px;
    box-sizing: border-box;

    & > div {
      margin-left: 24px;
      margin-right: 24px;
    }
  }

  .fill_item {
    margin-bottom: 18px;
    text-align: left;
    position: relative;
  }

  .select_one {
    margin-top: 0;
  }

  .select_wrap {
    position: relative;
  }

  /*输入框的样式修改*/
  /*设置输入框提示语的样式*/
  .fill_item input::placeholder {
    font-size: 32px;
    color: #919db3;
  }

  .fill_item textarea::placeholder {
    font-size: 32px;
    color: #919db3;
  }

  /* -----------------下拉选择菜单样式 --------------------*/
  .sticky-footer-container {
    background: #ffffff;
  }

}
</style>
