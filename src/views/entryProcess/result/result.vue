<template>
  <div v-if="fetchDone" class="flex-col page">
    <public-header
      :pageName="isRejected ? $t('title.reviewFailed') : $t('title.completed')"
      :showBackIcon="false"
      :fromRouteName="fromRouteName"
      headerColor="#fff"
    />
    <div class="content">
      <div class="flex-col group_3">
        <div class="flex-row justify-center">
          <img
            v-if="isRejected"
            class="image_5 status-icon"
            src="@/assets/images/entryProcess/result-failure.png"
          />
          <img
            v-else
            class="image_5 status-icon"
            src="@/assets/images/entryProcess/result-success.png"
          />
        </div>
        <div v-if="isRejected" class="flex-col items-center mt-54 desc">
          <span class="text_4">{{ $t('result.failedTitle') }}</span>
          <span  class="font_3 text_5">{{ $t('result.failedContent') }}: {{rejectedReason}}</span>
        </div>
        <div v-else class="flex-col items-center mt-54 desc">
          <span class="text_4">{{ $t('result.successTitle') }}</span>
          <span class="font_3 text_5">{{ $t('result.successContent') }}</span>
        </div>
      </div>
      <CButton v-if="isRejected" :name="$t('result.modifyNow')" @click="handleClick"/>
      <CButton v-else :name="$t('result.gotoHome')" @click="handleClick"/>
    </div>
  </div>
</template>

<script>
import CButton from "@/components/c-button.vue";
import { mapActions, mapState } from "vuex";
import { INVITING_STATUS_ENUMS } from "@/enums";

const { REJECTED_MANUAL, REJECTED_SYSTEM } = INVITING_STATUS_ENUMS;
let tmpFromPage = "";
export default {
  components: { CButton },
  props: {},
  data() {
    return {
      fetchDone: false,
      fromRouteName: '',
    };
  },
  computed: {
    ...mapState(['userInfo']),
    isRejected() {
      return [REJECTED_MANUAL, REJECTED_SYSTEM].includes(this.userInfo?.status);
    },
    rejectedReason() {
      if (this.userInfo?.status === REJECTED_SYSTEM) {
        return this.$t('result.rejectedSystem');
      }
      return this.userInfo?.reviewReasons || '';
    },
  },
  beforeRouteEnter(to, from, next) {
    tmpFromPage = from.name
    next()
  },
  // 离开路由
  beforeRouteLeave(to, from, next) {
    // 如果目标路由是 /home，直接放行，避免无限循环
    if (to.path === '/home') {
      next();
      return;
    }

    if (this.fromRouteName === 'bankCardInfo') {
      next('/home');
    } else {
      next();
    }
  },
  created() {
    this.fetchData();
    console.log("Result----fromRouteName", tmpFromPage);
    this.fromRouteName = tmpFromPage;
    this.$store.commit("SET_FROMROUTENAME", tmpFromPage);
  },
  methods: {
    ...mapActions(['fetchInviteInfo']),
    async fetchData() {
      await this.fetchInviteInfo();
      this.fetchDone = true;
      if (this.userInfo.status === INVITING_STATUS_ENUMS.INVITING) {
        // 未提交资料，回到填写资料首页
        this.isTriggerPush = true;
        this.$router.push('/personInfo');
      }
    },
    handleClick() {
      this.isTriggerPush = true;
      if (this.isRejected) {
        this.$router.push('/personInfo');
      } else {
        this.$router.push('/home');
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page {
  padding: 14px 0 440px;
  background-color: #ffffff;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}
.content {
  padding: 0 30px;
}
.group {
  padding: 0 16px 13px 18px;
  .group_2 {
    line-height: 14.5px;
    height: 14.5px;
    .font {
      font-size: 20px;
      line-height: 14.5px;
      color: #314250;
    }
    .text_2 {
      color: #314250;
      font-size: 20px;
      line-height: 10px;
    }
    .text {
      line-height: 14px;
    }
  }
  .image {
    width: 28px;
    height: 21px;
  }
  .image_2 {
    width: 17px;
    height: 20px;
  }
  .image_3 {
    width: 34px;
    height: 18px;
  }
}
.section {
  padding: 45px 38px 40.5px;
  background-color: #ffffff;
  .image_4 {
    width: 44px;
    height: 44px;
  }
  .pos {
    position: absolute;
    left: 38px;
    top: 50%;
    transform: translateY(-50%);
  }
  .text_3 {
    color: #1b3155;
    line-height: 34.5px;
  }
}
.group_3 {
  margin-top: 64px;
  .status-icon {
    width: 211px;
    height: 176px;
  }
  .desc {
    margin-bottom: 120px;
  }
  .text_4 {
    color: #1b3155;
    font-size: 48px;
    font-weight: 700;
    line-height: 46px;
    text-align: center;
  }
  .font_3 {
    font-size: 28px;
    line-height: 40px;
    color: #536786;
    text-align: center;
    font-style: normal;
    font-weight: 400;
  }
  .text_5 {
    margin-top: 29px;
  }
  .text_6 {
    margin-top: 20px;
  }
  .text_7 {
    margin-top: 21px;
  }
}
.view {
  margin: 116px 32px 0 32px;
  padding: 35px 0 37.5px;
  background-color: #0074ff;
  border-radius: 50px;
  .text_8 {
    color: #ffffff;
    line-height: 27.5px;
  }
}
.font_2 {
  font-size: 36px;
}
.modify-note {
  margin: 0 auto 24px;
  width: 600px;
  color: #7B8DA8;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 36px;
}
</style>
