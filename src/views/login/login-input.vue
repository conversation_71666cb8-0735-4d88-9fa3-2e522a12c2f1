<template>
  <div class="flex-col page">
    <div class="lang-selector-container">
      <lang-selector />
    </div>
    <div class="flex-col justify-start items-center self-center relative group_2">
      <img
        class="shrink-0 image_4"
        src="@/assets/images/login/logo.png"
      />
    </div>
    <div class="note">{{ $t('login.inputNote') }}</div>
    <div class="flex-row justify-center items-center self-stretch relative group_3">
      <palm-input v-model="email" placeholder="e.g. <EMAIL>" maxlength="100" :forbid-special-char="false"/>
      <img
        class="image_21 pos_17"
        src="../../assets/images/entryProcess/input-close.png"
        @click="resetEmail"
      />
    </div>
    <div class="flex-col justify-start self-stretch relative group_4">
      <CButton :name="$t('common.Next')" v-debounce="handleNextClick"/>
      <div class="personal-email-tip">{{ $t('login.personalEmailTip') }}</div>
    </div>
<!--    <div class="self-stretch group_5">
      <span class="font_2">Please read the terms</span>
      <a href="" class="font_3">
       Privacy Policy,Terms and Conditions, Date protection policy.
      </a>
      <span class="font_2 text_5">Login is deemed as reading and agreeing to the terms</span>
    </div>-->
  </div>
</template>

<script>
import PalmInput from "@/components/palm-input.vue";
import CButton from "@/components/c-button.vue";
import LangSelector from "@/components/lang-selector.vue";
import { checkEmail, sendEmailCode } from "@/axios/api";
import { EMAIL_REGEX } from "@/enums";

export default {
  components: { CButton, PalmInput, LangSelector },
  props: {},
  data() {
    return {
      email: '',
    };
  },
  created() {
    window.nativeBackCallback = function() {
      console.log('nativeBackCallback: return 0');
      // 1 原生不处理返回键  0 原生处理返回键
      return 0
    }
  },
  methods: {
    resetEmail() {
      this.email = '';
    },
    handleNextClick() {
      this.email = this.email?.trim() || '';
      if (!this.email) {
        this.$toast(this.$t('login.enterEmail'));
        return;
      }
      if (!EMAIL_REGEX.test(this.email)) {
        this.$toast(this.$t('login.enterValidEmail'));
        return;
      }
      this.checkEmail();
    },
    async checkEmail() {
      try {
        await checkEmail({
          email: this.email
        });
        await sendEmailCode({
          email: this.email
        })
        this.$store.commit('SET_EMAIL', this.email);
        this.$router.push('/login-otp');
      } catch (err) {
        console.log('🎉 ~ file: login-input.vue ~ line: 81 ~ err: ', err);
      }
    }
  },
};
</script>

<style scoped lang="scss">
.page {
  padding: 14px 48px 60px 48px;
  background-color: #ffffff;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  .lang-selector-container {
    position: absolute;
    top: 40px;
    right: 40px;
    z-index: 1;
  }
  .note {
    color: #1B3155;
    text-align: center;
    font-size: 28px;
    font-weight: 400;
    line-height: 40px;
  }
  .group {
    line-height: 14.5px;
    height: 14.5px;
    .font {
      font-size: 20px;
      line-height: 14.5px;
      color: #314250;
    }
    .text_2 {
      color: #314250;
      font-size: 20px;
      line-height: 10px;
    }
    .text {
      line-height: 14px;
    }
  }
  .image {
    width: 28px;
    height: 21px;
  }
  .image_2 {
    width: 17px;
    height: 20px;
  }
  .image_3 {
    width: 34px;
    height: 18px;
  }
  .group_2 {
    margin-top: 140px;
    padding: 8px 0 20px;
    overflow: hidden;
    width: 250px;
    .image_4 {
      width: 241px;
      height: 222px;
    }
  }
  .group_3 {
    margin-top: 24px;
    padding: 0 10px 0;
    :deep(.palm-input__inner) {
      text-align: center;
      padding: 0 40px;
      &:focus {
        border-bottom-color: #237BFF;
      }
    }
    .text_3 {
      color: #1b3155;
      font-size: 40px;
      line-height: 29px;
    }
    .image_21 {
      width: 34px;
      height: 34px;
    }
    .pos_17 {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .group_4 {
    flex: 1;
    margin-top: 55px;
    padding-top: 2px;
    .section {
      margin-left: 35px;
      margin-right: 35px;
      background-color: #1677ff7f;
      border-radius: 48.5px;
      filter: blur(9.5px);
      height: 97px;
    }
    .text-wrapper {
      padding: 37.5px 0 34.5px;
      background-color: #237bff;
      border-radius: 49px;
      .text_4 {
        color: #ffffff;
        font-size: 36px;
        line-height: 26px;
      }
    }
    .pos_18 {
      position: absolute;
      left: 31px;
      right: 31px;
      top: 0;
    }
    .personal-email-tip {
      margin-top: 20px;
      padding: 0 30px;
      color: #7b8da8;
      font-size: 24px;
      line-height: 32px;
      text-align: center;
    }
  }
  .group_5 {
    margin: 342px 17px 0 18.5px;
    line-height: 36px;
    text-align: center;
    .font_2 {
      font-size: 24px;
      line-height: 36px;
      color: #7b8da8;
    }
    .font_3 {
      font-size: 24px;
      line-height: 36px;
      font-weight: 700;
      color: #0074ff;
    }
    .text_5 {
      margin-left: 9.5px;
    }
  }
}
</style>
