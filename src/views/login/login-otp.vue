<template>
  <div class="flex-col page">
    <public-header />
    <div class="flex-col group_3 mt-183">
      <div class="flex-col items-start">
        <span class="text_3">{{ $t('login.enterOTP') }}</span>
        <div class="group_4 mt-33">
          <span class="font_2 text_4">{{ $t('login.otpSentTo') }} </span>
          <span class="font_2 text_5">{{ email }}</span>
        </div>
      </div>
      <div class="flex-col text-wrapper">
        <!-- 密码输入框 -->
        <password-input
          :value="otp"
          :length="4"
          :gutter="10"
          :mask="false"
          :focused="showKeyboard"
          @focus="showKeyboard = true"
        />
        <!-- 数字键盘 -->
        <number-keyboard
          v-model="otp"
          :show="showKeyboard"
          @blur="showKeyboard = false"
        />
      </div>
      <div class="flex-col group_5">
        <div class="flex-col justify-start self-stretch relative group_1">
          <CButton :disabled="isCounting" :name="btnText" @click="handleNext"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from "vuex";
import { PasswordInput, NumberKeyboard } from 'vant';
import CButton from "@/components/c-button.vue";
import { checkEmailCode, sendEmailCode } from "@/axios/api";
import { loginEM } from "@/api/native";
import { INVITING_STATUS_ENUMS } from "@/enums";

const defaultCountdown = 119;
export default {
  components: { CButton, PasswordInput, NumberKeyboard },
  props: {},
  data() {
    return {
      otp: '',
      showKeyboard: true,
      countdown: defaultCountdown, // 倒计时秒数
      isCounting: false, // 是否正在倒计时
      timer: null, // 用于保存setInterval的返回值
    };
  },
  computed: {
    ...mapState(['email', 'userInfo']),
    btnText() {
      if (this.isCounting) {
        return `${this.$t('login.getOtpAgain')} (${this.countdown}s)`;
      } else {
        return this.$t('login.getOtpAgain');
      }
    }
  },
  created() {
    if (!this.email) {
      this.$router.replace('/login-input')
    }
  },
  mounted() {
    this.startCountdown();
  },
  methods: {
    ...mapActions(['fetchInviteInfo', 'setToken', 'setTempToken', 'removeAllToken']),
    async handleNext() {
      if (this.isCounting) return; // 如果已经在倒计时，点击无效
      await sendEmailCode({
        email: this.email
      })
      this.startCountdown();
    },
    startCountdown() {
      this.isCounting = true; // 开始倒计时
      this.timer = setInterval(() => {
        if (this.countdown > 1) {
          this.countdown--;
        } else {
          clearInterval(this.timer); // 倒计时结束，清除定时器
          this.isCounting = false; // 允许重新发送
          this.countdown = defaultCountdown; // 重置倒计时
        }
      }, 1000); // 每秒更新一次倒计时
    },
    async checkEmailCode() {
      try {
        const data = await checkEmailCode({
          email: this.email,
          code: this.otp
        })
        const loginEMData = {
          uid: data.id,
          token: data.token || data.tempToken,
          account: this.email,
        }
        this.$store.commit('SET_UID', data.id);
        if (data.tempToken) {
          loginEM(loginEMData);
          this.removeAllToken();
          this.setTempToken(data.tempToken);
          await this.fetchInviteInfo();
          if (this.userInfo?.status === INVITING_STATUS_ENUMS.INVITING) {
            this.$router.push('/personInfo');
          } else {
            this.$router.push('/home');
          }
        } else if (data.token) {
          loginEM(loginEMData);
          this.removeAllToken();
          this.setToken(data.token);
          this.$router.push('/home');
        };
      } catch (err) {
        console.log('🎉 ~ file: login-otp.vue ~ line: 99 ~ err: ', err);
        this.otp = '';
        /*clearInterval(this.timer); // 倒计时结束，清除定时器
        this.isCounting = false; // 允许重新发送
        this.countdown = defaultCountdown; // 重置倒计时*/
      }
    }
  },
  watch: {
    otp(val) {
      if (val?.trim().length === 4) {
        this.checkEmailCode();
      }
    }
  },
  beforeDestroy() {
    // 确保在组件销毁时清除定时器，避免内存泄漏
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
};
</script>

<style scoped lang="scss">
.mt-35 {
  margin-top: 35px;
}
.mt-183 {
  margin-top: 183px;
}
.mt-33 {
  margin-top: 33px;
}
.mt-37 {
  margin-top: 37px;
}
.page {
  padding: 14px 0 392.5px;
  background-color: #ffffff;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  .group {
    padding-left: 18px;
    padding-right: 16px;
    .group_2 {
      line-height: 14.5px;
      height: 14.5px;
      .font {
        font-size: 20px;
        line-height: 14.5px;
        color: #314250;
      }
      .text_2 {
        color: #314250;
        font-size: 20px;
        line-height: 10px;
      }
      .text {
        line-height: 14px;
      }
    }
    .image {
      width: 28px;
      height: 21px;
    }
    .image_2 {
      width: 17px;
      height: 20px;
    }
    .image_3 {
      width: 34px;
      height: 18px;
    }
    .image_4 {
      margin-left: 14px;
      width: 44px;
      height: 44px;
    }
  }
  .group_3 {
    padding: 0 48px;
    .text_3 {
      margin-left: 2.5px;
      color: #1b3155;
      font-size: 40px;
      font-weight: 700;
      line-height: 38.5px;
    }
    .group_4 {
      line-height: 24px;
      .font_2 {
        font-size: 28px;
        color: #1b3155;
      }
      .text_4 {
        line-height: 24px;
      }
      .text_5 {
        font-weight: 700;
        line-height: 20.5px;
      }
    }
    .text-wrapper {
      margin-top: 108px;
      :deep(.palm-input__inner) {
        padding: 0;
        &:focus {
          border-bottom-color: #237BFF;
        }
      }
      :deep(.van-password-input__item) {
        background-color: #F1F3F6;
        border-radius: 20px;
      }
    }
    .group_5 {
      margin-top: 55px;
      .group_1 {
        padding-top: 2px;
        .section {
          margin-left: 4px;
          margin-right: 4px;
          background-color: #1677ff7f;
          border-radius: 48.5px;
          filter: blur(9.5px);
          height: 97px;
        }
        .text-wrapper_2 {
          padding: 36.5px 0 27.5px;
          background-color: #237bff;
          border-radius: 49px;
          .text_7 {
            color: #ffffff;
            line-height: 34px;
          }
        }
        .pos {
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
        }
      }
      .group_6 {
        .font_4 {
          font-size: 24px;
          line-height: 40px;
        }
        .text_8 {
          color: #7b8da8;
        }
        .text_9 {
          color: #237bff;
          text-decoration: underline;
        }
      }
    }
    .font_3 {
      font-size: 36px;
    }
  }
}
</style>
