import store from '../store'
import { getQueryString } from "@/utils/tools";

export const hasProduct = () => {
  const params = getQueryString()
  // eslint-disable-next-line no-prototype-builtins
  if (Object.getOwnPropertyNames(params).length>0 && params.hasOwnProperty('product')) {
    return true
  } else {
    return false
  }
}

export const initFun = () => {
  console.log('执行initFun.js==============')
  console.log('当前url地址--', location.href)
  const params = getQueryString()
  if (hasProduct()) {
    if (params.product) {
      params.product = params.product.toLowerCase()
    }
    const productSource = params.product
    localStorage.setItem('productSource', productSource);

    if (params.enterSource === 'app') {
      console.debug(`入口来源：${params.enterSource} - loanId：${params.loanId} - 产品渠道-包名：${params.product}`)
      localStorage.setItem('enterSource', params.enterSource);
      store.commit('SET_ENTERSOURCE', params.enterSource)

      if (params.autoCredit !== undefined) {
        localStorage.setItem('autoCredit', params.autoCredit)
      } else {
        localStorage.setItem('autoCredit', 'false')
      }
    }
  }
}
