<template>
  <div id="app">
    <!-- 调试刷新组件 -->
    <DebugRefresh />

    <template>
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive" />
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive" />
    </template>
  </div>
</template>
<script>
import qs from "qs";
import { mapState } from "vuex";
import DebugRefresh from "@/components/debug-refresh.vue";

export default {
  name: "App",
  components: {
    DebugRefresh
  },
  data() {
    return {
      visible: true,
      productSource: ''
    }
  },
  computed: {
    ...mapState(['themeColor', 'logoutDialogVisible'])
  },
  created() {
    console.log('当前页面地址: ', location.href);
    this.productSource = localStorage.getItem('productSource') || 'default';
    const queryString = location.href.split('?')[1];
    if (queryString) {
      const queryParams = qs.parse(queryString); // 使用 qs.parse 解析
      const { from, country } = queryParams || {};
      if (from) {
        localStorage.setItem('from', from);
      }
      if (country) {
        localStorage.setItem('country', country);
      }
    }
  },
  mounted() {
    document.getElementsByTagName("body")[0].setAttribute("data-theme", this.productSource);
  }
}
</script>
<style src="@/assets/css/reset.css"></style>
<style src="@/assets/css/common.css"></style>
<style lang="scss" scoped>
/deep/ .van-popup--center {
  top: 0;
  left: 0;
  transform: initial;
  width: 100%;
  height: 100%;
  background: none;
}

/deep/ .van-overlay {
  background: rgba(0,0,0,0.50);
}
</style>
<style>
#app {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
}


</style>

