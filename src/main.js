import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import '@/utils/rem.js';
import "@/validators";
import global from './components/global'
import loading001 from '@/utils/loading'
import { singleToThousands, thousandsToFixed, thousandsToFloat } from '@/utils/tools.js';
import { showToast, getAppConfig } from '@/api/native';
import {
  List,
  Popup,
  Overlay,
  Popover,
  Loading,
  PullRefresh,
  Toast
} from 'vant'
import { i18n, vantLocales, LANG_MAP } from '@/i18n'
// 引入 v-calendar
import VCalendar from 'v-calendar'
// 引入语言文件用于 v-calendar 配置
import enLocale from '@/i18n/locale/en'
import zhLocale from '@/i18n/locale/zh'
import esLocale from '@/i18n/locale/es'
import frLocale from '@/i18n/locale/fr'
import swLocale from '@/i18n/locale/sw'
import twLocale from '@/i18n/locale/tw'
//注册全局指令
import directives from '@/utils/directives.js'
Vue.use(directives)
if (process.env.VUE_APP_CONSOLE === 'true') {
  console.log('[Eruda] 开始加载调试工具...');
  import('eruda')
    .then(eruda => {
      console.log('[Eruda] 初始化成功，版本：', eruda.version);
      eruda.default.init();
    })
    .catch(error => {
      console.error('[Eruda] 加载失败:', error);
    });
}

// vant-ui组件国际化
vantLocales(i18n.locale)

// eslint-disable-next-line promise/catch-or-return
getAppConfig().then(res => {
  // 确认res.language是否在LANG_MAP中
  const resLang = Object.keys(LANG_MAP).includes(res.language) ? res.language : 'en'
  // 优先使用本地存储的语言设置
  const localLang = localStorage.getItem('lang')
  const lang = localLang || resLang

  i18n.locale = lang
  vantLocales(lang)

  if (!localLang && resLang) {
    localStorage.setItem('lang', resLang)
  }

  if (res.deviceId) {
    setTimeout(() => {
      store.commit('SET_DEVICEID', res.deviceId)
      store.commit('SET_DEVICE_TYPE', res.deviceType)
    })
  }
}).catch(err => {
  console.error('getAppConfig error', err)
  // 发生错误时使用默认语言
  const lang = localStorage.getItem('lang') || 'en'
  i18n.locale = lang
  vantLocales(lang)
})

Vue.config.productionTip = false
// 捕获未知promise reject异常，通常来自于第三库的报错.
window.onunhandledrejection = function (e) {
  console.log(e)
  e.preventDefault()
}

// 增加资源捕获错误
window.addEventListener('error', event => {
  const target = event.target;
  const isElementTarget = target instanceof HTMLScriptElement || target instanceof HTMLLinkElement;
  if (!isElementTarget) return false;

  showToast(i18n.t('toastMsg.loadResourceFailed'));
  setTimeout(() => {
    location.href = '/';
  }, 2000);
}, true);

Vue.prototype.common = {
  singleToThousands,
  thousandsToFixed,
  thousandsToFloat,
};
Vue.prototype.$eventBus = new Vue()
//注册全局货币过滤器
Vue.filter('singleToThousands', function (value) {
  return singleToThousands(value);
});
Vue.filter('thousandsToFixed', function (value) {
  return thousandsToFixed(value);
});

Vue.use(Popup)
Vue.use(Overlay)
Vue.use(Popover)
Vue.use(Loading)
Vue.use(PullRefresh)
Vue.use(List)
Vue.use(Toast)
Vue.use(global);
Vue.use(loading001)
// 注册 v-calendar 组件
Vue.use(VCalendar, {
  componentPrefix: 'v',
  locales: {
    'en-US': enLocale.calendar.locale,
    'zh-CN': zhLocale.calendar.locale,
    'es-ES': esLocale.calendar.locale,
    'fr-FR': frLocale.calendar.locale,
    'sw-SW': swLocale.calendar.locale,
    // 对于少数语言，使用英语 locale 作为兜底，但保持自定义的 id
    tw: {
      ...enLocale.calendar.locale,
    }
  }
})

new Vue({
  i18n,
  router,
  store,
  render: h => h(App)
}).$mount('#app')
