@import "./variable.scss";
@import "./theme/default/default.scss";

$themes: (
  default: $theme-default,
);

$theme-map: null;
@function themed($key) {
  @return map-get($theme-map, $key);
}

// 第三步: 定义混合指令, 切换主题,并将主题中的所有规则添加到theme-map中
@mixin themify() {
  @each $theme-name, $map in $themes {
    // & 表示父级元素  !global 表示覆盖原来的
    // !global 把局部变量强升为全局变量
    // #{}是sass的插值表达式
    [data-theme="#{$theme-name}"] & {
      $theme-map: () !global;
      // 循环合并键值对
      @each $key, $value in $map {
        $theme-map: map-merge(
          $theme-map,
          (
            $key: $value,
          )
        ) !global;
      }
      // 表示包含 下面函数 themed()
      @content;
    }
  }
}

.c-button {
  @include themify() {
    color: themed("button-color");
  }

  @include themify() {
    background: themed("button-background");
  }
}

.c-button:active {
  @include themify() {
    background: themed("button-background");
  }
}

.c-button.disabled {
  @include themify() {
    background: themed("button-disable-background");
  }
}
.c-button.disabled-btn {
  @include themify() {
    background: themed("button-disable-background");
  }
}

