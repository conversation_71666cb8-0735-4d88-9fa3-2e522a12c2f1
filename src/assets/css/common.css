/******* 公共样式  ********/

.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}

.clearfix {
    *zoom: 1;
}

.bg-fff {
    background-color: #fff;
}

.flx-block {
    display: flex;
}

.c-page-pd {
    padding: 112px 0 60px;
}

.c-page-24 {
    margin: 0 24px;
    padding-bottom: 70px;
}

.c-page-32 {
    margin: 0 32px;
}

.header-text {
    font-size: 24px;
    text-align: left;
    color: #919db3;
    line-height: 36px;
    margin: 24px 0;
}

.header-text.ps-ma {
    margin: 12px 0 24px;
}

.header-text.ps-ma.red {
    color: #E84E40;
}

.header-text em {
    color: #FFA600;
}

.c-button {
    width: 100%;
    height: 90px;
    line-height: 90px;
    font-size: 36px !important;
    border-radius: 42px;
}

.c-button:active {
}

.c-button.disable {
    cursor: not-allowed;
    pointer-events: none;
    box-shadow: none;
}
.c-button.disable-btn {
  cursor: not-allowed;
  pointer-events: none;
  box-shadow: none;
}
.fill_phone_length {
    font-size: 20px;
    color: #999999;
    position: absolute;
    right: 25px;
    bottom: 18px;
}

.fill_phone_length>em {
    color: #fe5b6e;
}

.fill_phone_length.position {
    bottom: 35px;
}


/*下拉选择框错误，样式*/

.error_input_border input {
    border-bottom: 1px solid #E84E40 !important;
  }

  .error_input_border .cancleTipClass input {
    border: 2px solid transparent !important;
  }

  .error_input_border .field-textarea textarea {
    border: 2px solid #E84E40 !important;
  }

  .error_input_border .field {
    border: 2px solid #e84e40;
  }
  .cancle_error_input_border.field{
    border-bottom: 0.028rem solid #ccc;
  }
  .cancle_error_input_border.field-textarea textarea {
    border: 2px solid #C4CAD5 !important;
  }
  .cancle_error_input_border.field-number input {
    border: 2px solid #C4CAD5 !important;
  }


/* 协议-合同全局样式 */

.agreement {
    margin-top: 8px;
    padding: 0 40px 20px;
    color: #666;
    font-size: 26px;
    line-height: 1.4;
}

.agreement>p {
    margin-bottom: 10px;
}

.agreement>p strong {
    font-weight: bold;
}

.agreement .title {
    font-size: 32px;
    font-weight: bold;
    margin-top: 10px;
    margin-bottom: 20px;
    color: #1753A4;
}

.van-toast--text {
    line-height: 40px;
    padding: 15px 24px !important;
    font-size: 24px !important;
    z-index: 5001 !important;
    word-break: normal;
}

.toast-style {
    max-width: 100%!important;
    width: 85%!important;
}

.invalid {
    font-size: 28px;
    font-weight: 500;
    text-align: center;
    color: #ffa600;
    line-height: 33px;
}


/* 头部导航-问号图标

.nav-question-mark::after {
    content: "";
    width: 48px;
    height: 48px;
    display: inline-block;
    border-radius: 50%;
    background: url('../../assets/images/coupons/bg_refund_ticket_question_title.png') no-repeat;
    background-size: 40px 40px;
    margin: 38px 0px 0px 290px;
}*/

.popup-content-show {
    margin: 0 56px;
    transform: translateY(50%);
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 32px 0 rgba(0, 0, 0, 0.40);
    padding: 40px 32px;
    position: relative;
}
.popup-content-show > h1 {
    font-size: 48px;
    font-weight: 700;
    text-align: left;
    color: #1b3155;
    line-height: 57px;
    text-shadow: 0 2px 32px 0 rgba(0,0,0,0.40);
    margin: 0 0 35px 0;
}
.popup-content-show .title {
    font-size: 32px;
    font-weight: 400;
    text-align: center;
    color: #536887;
    line-height: 45px;
    text-shadow: 0 2px 32px 0 rgba(0,0,0,0.40);
}
.popup-content-show > img {
    width: 234px;
    margin: 30px auto 0;
}
.popup-content-show .pop-img {
    width: 350px;
    height: 188px;
    position: absolute;
    right: 0;
    top: -82px;
}
.popup-content-show .c-button {
    margin-top: 50px;
}
.popup-content-show > h3 {
    font-size: 28px;
    font-weight: 500;
    text-align: center;
    color: #3a97ff;
    line-height: 33px;
    text-shadow: 0 2px 32px 0 rgba(0,0,0,0.40);
    margin-top: 50px;
}

.fill_item .textarea_wrap:after, .fill_item .select_wrap:after {
    content: attr(data-error);
    color: #F00;
    font-size: 24px;
    margin-top: 6px;
    padding-left: 24px;
}
