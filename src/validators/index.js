import { extend } from 'vee-validate';
import { required, min, max } from 'vee-validate/dist/rules';
import { i18n } from '@/i18n';

//自定义提示

extend('required', {
    ...required,
    message: (field) => `${i18n.t("testMsg.notFill", { field })}`
})

// selectRequired
extend('requiredSelect', {
    ...required,
    message: (field) => `${i18n.t("testMsg.notSelect", { field })}`
})

extend('requiredPe', {
    ...required,
    message: (field) => "No puede estar vacío"
})

extend('min', min)
extend('max', max)

/*---------------------自定义校验规则 start-------------------------------*/
//Email Address 校验
extend('emailTest', {
    message: field => i18n.t("testMsg.pleseInputCorrect"),
    validate: value => /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(value)
});
//Phone Number 校验
extend('phoneTest', {
    message: field => i18n.t("testMsg.pleseInputCorrect"),
    validate: value => /^[2-5]\d{8}$/.test(value)
});
//限制只能输入一个小数点，并且第一位和最后一位不能是小数点
extend('digitalTest', {
    message: field => i18n.t("testMsg.wrongCell"),
    validate: value => /^(0|[1-9][0-9]*?)(\.[0-9]{1,2})?$/.test(value)
});
//living数组校验
extend('livingTest', {
    message: field => i18n.t("testMsg.canNotEmpty"),
    validate: value => value.length > 0
});
// 数字验证
extend('numberTest', {
    message: field => i18n.t("testMsg.pleaseInputNumber"),
    validate: value => /^[0-9]*$/.test(value)
});
/*---------------------自定义校验规则 end-------------------------------*/
