import Vue from 'vue'
import Vuex from 'vuex'
import state from "@/store/state";
import mutations from "@/store/mutations";
import actions from "@/store/actions";
import getters from './getters'
import report from './modules/report'
import coupons from './modules/coupons'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    report,
    coupons
  },
  state,
  mutations,
  actions,
  getters
})

export default store
