const state = {
  couponItem: {},
  couponList: [],
  fromCouponPage: '' // 标记从哪个页面进入优惠券列表页面，提现页面或还款页面进入优惠券列表，用于返回
}

const getters = {
  couponItem: state => state.couponItem,
  couponList: state => state.couponList,
  fromCouponPage: state => state.fromCouponPage
}

const mutations = {
  SET_COUPONITEM: (state, couponItem) => {
    state.couponItem = couponItem
    window.sessionStorage.setItem("couponItem", JSON.stringify(couponItem))
  },
  SET_COUPONLIST: (state, couponList) => {
    state.couponList = couponList
    window.sessionStorage.setItem("couponList", JSON.stringify(couponList))
  },
  SET_FROMCOUPONPAGE: (state, fromCouponPage) => {
    state.fromCouponPage = fromCouponPage
    window.sessionStorage.setItem("fromCouponPage", fromCouponPage)
  }
}

const actions = {

}

export default {
  namespaced: true,
  state,
  actions,
  getters,
  mutations
}