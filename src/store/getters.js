const getters = {
  //  获取当前路由信息
  route: state => {
    return state.route
  },
  // 考勤规则相关 getters
  attendanceRules: state => state.attendanceRules,
  attendanceRulesLoaded: state => state.attendanceRulesLoaded,
  // 是否为限制考勤类型
  isRestrictedAttendance: state => state.attendanceRules.clockType === 1,
  // 考勤位置列表
  clockLocationList: state => state.attendanceRules.clockLocationList || [],
  // 当前打卡信息相关 getters
  currentSignInfo: state => state.currentSignInfo,
  currentSignInfoLoaded: state => state.currentSignInfoLoaded,
  // 是否已签到（只使用 currentSignInfo 数据源）
  isClockIn: state => !!state.currentSignInfo?.clockInTime,
  // 是否已签退（只使用 currentSignInfo 数据源）
  isClockOut: state => !!state.currentSignInfo?.clockOutTime,
  // 上班打卡状态（直接使用 currentSignInfo 返回的状态值）
  onStatus: state => state.currentSignInfo?.onStatus,
  // 下班打卡状态（直接使用 currentSignInfo 返回的状态值）
  offStatus: state => state.currentSignInfo?.offStatus,
  // 考勤统计数据（从 currentSignInfo 获取）
  attendanceStats: state => ({
    lateArrivalNum: state.currentSignInfo?.lateArrivalNum || 0,
    leavingEarlyNum: state.currentSignInfo?.leavingEarlyNum || 0,
    notClockingInNum: state.currentSignInfo?.notClockingInNum || 0
  })
}

export default getters
