/**
 * 虚拟键盘处理工具
 * 用于解决移动端虚拟键盘弹出时输入框被遮挡的问题
 */
import { isIOSNonSafari } from "@/utils/tools";

class VirtualKeyboardHandler {
  constructor(options = {}) {
    this.targetSelector = options.targetSelector || '.credit-index'; // 目标容器选择器
    this.focusPadding = options.focusPadding || 400; // 聚焦时的padding值
    this.defaultPadding = options.defaultPadding || 0; // 默认padding值
    this.isListening = false;
    this.consecutiveInputClicks = 0; // 连续点击输入框的次数

    // 绑定方法的this上下文
    this.handleGlobalFocusIn = this.handleGlobalFocusIn.bind(this);
    this.handleGlobalFocusOut = this.handleGlobalFocusOut.bind(this);
  }

  /**
   * 开始监听输入框焦点事件
   */
  startListening() {
    if (this.isListening) return;

    document.addEventListener('focusin', this.handleGlobalFocusIn);
    document.addEventListener('focusout', this.handleGlobalFocusOut);
    this.isListening = true;

    console.log('VirtualKeyboardHandler: Started listening for focus events');
  }

  /**
   * 停止监听输入框焦点事件
   */
  stopListening() {
    if (!this.isListening) return;

    document.removeEventListener('focusin', this.handleGlobalFocusIn);
    document.removeEventListener('focusout', this.handleGlobalFocusOut);
    this.isListening = false;

    // 停止监听时恢复默认padding
    this.setTargetPadding(this.defaultPadding);

    console.log('VirtualKeyboardHandler: Stopped listening for focus events');
  }

  /**
   * 处理全局焦点进入事件
   * @param {Event} event 焦点事件
   */
  handleGlobalFocusIn(event) {
    const activeElement = event.target;
    const isInputOrTextarea = this.isInputElement(activeElement);

    if (isInputOrTextarea) {
      // 增加连续点击计数
      this.consecutiveInputClicks++;

      // 只有当计数器达到2或以上时，才执行设置 padding-bottom 的逻辑
      if (this.consecutiveInputClicks >= 2) {
        this.setTargetPadding(this.focusPadding);
        console.log(`VirtualKeyboardHandler: Input/Textarea focused (click #${this.consecutiveInputClicks}), set padding-bottom to ${this.focusPadding}px`);
      } else {
        console.log(`VirtualKeyboardHandler: Input/Textarea focused (click #${this.consecutiveInputClicks}), skipping padding adjustment`);
      }
    }
  }

  /**
   * 处理全局焦点离开事件
   * @param {Event} event 焦点事件
   */
  handleGlobalFocusOut(event) {
    // 延迟检查，确保新的焦点元素已经设置
    setTimeout(() => {
      const activeElement = document.activeElement;
      const isInputOrTextarea = this.isInputElement(activeElement);

      // 如果当前没有输入框或文本域处于焦点状态，恢复默认padding并重置计数器
      if (!isInputOrTextarea) {
        this.consecutiveInputClicks = 0; // 重置连续点击计数器
        this.setTargetPadding(this.defaultPadding);
        console.log(`VirtualKeyboardHandler: No input/textarea focused, restore padding-bottom to ${this.defaultPadding}px and reset click counter`);
      }
    }, 100);
  }

  /**
   * 检查元素是否为输入元素
   * @param {Element} element DOM元素
   * @returns {boolean} 是否为输入元素
   */
  isInputElement(element) {
    return element && (
      element.tagName === 'INPUT' ||
      element.tagName === 'TEXTAREA'
    );
  }

  /**
   * 设置目标元素的padding-bottom
   * @param {number} paddingValue padding值
   */
  setTargetPadding(paddingValue) {
    const targetElement = document.querySelector(this.targetSelector);
    if (targetElement) {
      targetElement.style.paddingBottom = `${paddingValue}px`;
    } else {
      console.warn(`VirtualKeyboardHandler: Target element not found: ${this.targetSelector}`);
    }
  }

  /**
   * 更新配置
   * @param {Object} options 新的配置选项
   */
  updateOptions(options = {}) {
    if (options.targetSelector !== undefined) {
      this.targetSelector = options.targetSelector;
    }
    if (options.focusPadding !== undefined) {
      this.focusPadding = options.focusPadding;
    }
    if (options.defaultPadding !== undefined) {
      this.defaultPadding = options.defaultPadding;
    }

    console.log('VirtualKeyboardHandler: Options updated', {
      targetSelector: this.targetSelector,
      focusPadding: this.focusPadding,
      defaultPadding: this.defaultPadding
    });
  }

  /**
   * 销毁实例
   */
  destroy() {
    this.stopListening();
    console.log('VirtualKeyboardHandler: Instance destroyed');
  }
}

/**
 * 创建虚拟键盘处理器实例
 * @param {Object} options 配置选项
 * @param {string} options.targetSelector 目标容器选择器，默认'.credit-index'
 * @param {number} options.focusPadding 聚焦时的padding值，默认650
 * @param {number} options.defaultPadding 默认padding值，默认45
 * @returns {VirtualKeyboardHandler} 处理器实例
 */
export function createVirtualKeyboardHandler(options = {}) {
  return new VirtualKeyboardHandler(options);
}

/**
 * Vue Mixin - 虚拟键盘处理
 * 可以直接在Vue组件中使用
 */
export const virtualKeyboardMixin = {
  data() {
    return {
      virtualKeyboardHandler: null,
      virtualKeyboardOptions: 600,
      defaultPadding: 0,
      consecutiveInputClicks: 0, // 连续点击输入框的次数
    };
  },

  mounted() {
    if (isIOSNonSafari()) {
      // 创建虚拟键盘处理器
      this.virtualKeyboardHandler = createVirtualKeyboardHandler(this.virtualKeyboardOptions || {});
      this.virtualKeyboardHandler.startListening();

      // 添加全局焦点事件监听器来跟踪连续点击
      document.addEventListener('focusin', this.handleInputFocusIn);
      document.addEventListener('focusout', this.handleInputFocusOut);
    }
  },

  beforeDestroy() {
    // 清理虚拟键盘处理器
    if (this.virtualKeyboardHandler) {
      this.virtualKeyboardHandler.destroy();
      this.virtualKeyboardHandler = null;
    }

    // 移除全局焦点事件监听器
    if (isIOSNonSafari()) {
      document.removeEventListener('focusin', this.handleInputFocusIn);
      document.removeEventListener('focusout', this.handleInputFocusOut);
    }
  },

  methods: {
    /**
     * 处理输入框焦点进入事件
     * @param {Event} event 焦点事件
     */
    handleInputFocusIn(event) {
      const activeElement = event.target;
      const isInputOrTextarea = this.isInputElement(activeElement);

      if (isInputOrTextarea) {
        // 增加连续点击计数
        this.consecutiveInputClicks++;
        console.log(`Mixin: Input/Textarea focused (click #${this.consecutiveInputClicks})`);
      }
    },

    /**
     * 处理输入框焦点离开事件
     * @param {Event} event 焦点事件
     */
    handleInputFocusOut(event) {
      // 延迟检查，确保新的焦点元素已经设置
      setTimeout(() => {
        const activeElement = document.activeElement;
        const isInputOrTextarea = this.isInputElement(activeElement);

        // 如果当前没有输入框或文本域处于焦点状态，重置计数器
        if (!isInputOrTextarea) {
          this.consecutiveInputClicks = 0;
          console.log('Mixin: No input/textarea focused, reset click counter');
        }
      }, 100);
    },

    /**
     * 检查元素是否为输入元素
     * @param {Element} element DOM元素
     * @returns {boolean} 是否为输入元素
     */
    isInputElement(element) {
      return element && (
        element.tagName === 'INPUT' ||
        element.tagName === 'TEXTAREA'
      );
    }
  }
};

export default VirtualKeyboardHandler;
