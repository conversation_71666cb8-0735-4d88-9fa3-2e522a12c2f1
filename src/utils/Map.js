//用于检查进件步骤中所有电话号码是否重复的问题

class IMap {
  constructor(label) {
    this.map = new Map()
    this.label = label
  }

  add (key, value) {
    // key在map中已经存在，说明是编辑操作替换掉原有的号码
    if (this.map.has(key)) {
      this.map.delete(key)
    }
    //如果电话为空，则不添加
    if (!value) return null
    this.map.set(key, value)
  }

  findRepeat (key, value, options = {}) {
    const { ignore = [] } = options
    let result = false
    console.log('findRepeat', this.map)
    for (const [k, v] of this.map) {
      if (value === v && key !== k && ignore.indexOf(k) === -1) {
        result = true
        return result
      }
    }
    return result
  };

  findAllRepeat () {
    const values = []
    const repeatValues = new Set()
    const repeatKey = []
    const msgArr = []
    for (const [key, value] of this.map) {
      console.log(key)
      if (values.indexOf(value) !== -1) {
        repeatValues.add(value)
      } else {
        values.push(value)
      }
    }
    for (const repeat of repeatValues) {
      const msg = []
      for (const [key, value] of this.map) {
        if (value === repeat) {
          repeatKey.push(key)
          msg.push(key)
        }
      }
      msgArr.push(`${msg.toString()} are the same`)
    }
    return [repeatKey, `${msgArr.toString()},Please input different ${this.label}`]
  }
}

const phoneMap = new IMap('phone No')
const nameMap = new IMap('Name')

export {
  phoneMap,
  nameMap
}
