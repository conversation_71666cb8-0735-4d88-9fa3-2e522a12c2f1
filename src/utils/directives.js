import debounce from 'lodash/debounce';

// 滚动到输入框的函数
function scrollToInput(el) {
  // 找到输入框的父元素
  const parent = el.offsetParent;

  if (parent) {
      // 计算输入框距离其父元素顶部的位置
      const offsetTop = el.offsetTop;

      // 滚动父元素，使得输入框顶部与父元素顶部对齐
      parent.scrollTop = offsetTop;
  }
}

export default Vue => {
  // 创建一个名为`focusable`的自定义指令
  Vue.directive('focusable', {
    // 当绑定到元素的指令被绑定时执行
    inserted: function (el, binding, vnode) {
        // 确保el是input元素
        if (el.tagName === 'INPUT') {
            el.focus(); // 立即获取焦点（可选）

            el.addEventListener('focus', function () {
                // 滚动到当前输入框
                scrollToInput(el);
            });
        }
    }
  });

  // 添加防抖指令
  Vue.directive('debounce', {
    inserted(el, binding) {
      let handler;
      let delay = 500; // 默认延迟500ms

      // 解析绑定值
      if (Array.isArray(binding.value)) {
        // 数组形式：[handler, delay]
        [handler, delay] = binding.value;
      } else if (typeof binding.value === 'object') {
        // 对象形式：{ handler, delay }
        handler = binding.value.handler;
        delay = binding.value.delay || delay;
      } else {
        // 函数形式：直接传入处理函数
        handler = binding.value;
      }

      // 确保处理函数是一个函数
      if (typeof handler !== 'function') {
        console.warn('v-debounce 指令需要一个函数作为参数');
        return;
      }

      // 创建防抖函数
      const debouncedHandler = debounce(handler, delay);

      // 保存引用
      el._debounceValue = handler;
      el._debounceDelay = delay;
      el._debouncedHandler = debouncedHandler;

      // 添加点击事件监听器
      el.addEventListener('click', debouncedHandler);
    },

    unbind(el) {
      // 清理防抖处理器
      if (el._debouncedHandler) {
        el.removeEventListener('click', el._debouncedHandler);
        el._debouncedHandler.cancel();
        delete el._debouncedHandler;
        delete el._debounceValue;
        delete el._debounceDelay;
      }
    },
  });
}

