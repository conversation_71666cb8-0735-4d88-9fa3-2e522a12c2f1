/*function base64ToBlob(base64, contentType) {
  const byteCharacters = atob(base64); // 解码 Base64
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: contentType });
}*/

export const base64ToBlob = (base64, contentType= '', sliceSize= 512) => {
  const byteCharacters = atob(base64);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    const slice = byteCharacters.slice(offset, offset + sliceSize);
    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  const blob = new Blob(byteArrays, { type: contentType });
  return blob;
}

/**
 * {
 *     file
 *     "fileType": "",
 *     "fileSize": "",
 *     "fileName": "",
 *     "filePath": "",
 *     "base64": ""
 * }
 */
export function base64ToFormData(file) {
  // file 容错
  if (!file) return;
  // 将 Base64 数据转换为 Blob
  const blob = base64ToBlob(file.base64, file.fileType);
  const formData = new FormData();
  formData.append('file', blob, file.fileName);
  return formData;
}
