// Mock数据场景枚举
export const MOCK_SCENARIOS = {
  NORMAL: 'NORMAL', // 正常状态
  NO_GROUP: 'NO_GROUP', // 未配置考勤组
  NO_GPS: 'NO_GPS', // 无GPS权限
  OUT_RANGE: 'OUT_RANGE', // 不在打卡范围内
  IN_RANGE: 'IN_RANGE', // 在打卡范围内
  LATE_CLOCK_IN: 'LATE_CLOCK_IN', // 迟到打卡
  EARLY_CLOCK_OUT: 'EARLY_CLOCK_OUT', // 早退打卡
  BOTH_CLOCKED: 'BOTH_CLOCKED', // 已完成上下班打卡
  NO_CLOCK: 'NO_CLOCK', // 未打卡
  FORGOT_CLOCK_IN: 'FORGOT_CLOCK_IN', // 忘记打上班卡
  HOLIDAY: 'HOLIDAY' // 假期模式
};

/**
 * 获取指定场景的Mock数据
 * @param {string} scenario - 场景枚举值
 * @param {string} currentDate - 当前日期
 * @returns {Object} 包含attendanceRules、currentSignInfo、gpsInfo、wifiInfo的对象
 */
export function getMockData(scenario, currentDate) {
  const baseWifiInfo = {
    ssid: 'Office-WiFi',
    bssid: 'aa:bb:cc:dd:ee:ff',
    networkType: 'Wi-Fi'
  };

  const baseAttendanceRules = {
    attendanceName: '总部考勤组',
    clockType: 0,
    workStartTime: '09:00:00',
    workEndTime: '18:00:00',
    breakFlag: 1,
    workFlag: 1
  };

  const baseGpsInfo = { latitude: '39.9042', longitude: '116.4074' };

  const baseCurrentSignInfo = {
    attendanceDate: currentDate,
    clockInTime: null,
    clockOutTime: null,
    onStatus: null,
    offStatus: null
  };

  switch (scenario) {
    case MOCK_SCENARIOS.NO_GROUP:
      // 未配置考勤组
      return {
        attendanceRules: {
          ...baseAttendanceRules,
          attendanceName: '', // 考勤组名称为空
          attendanceRule: '',
          clockType: 1,
          clockLocationList: []
        },
        currentSignInfo: baseCurrentSignInfo,
        gpsInfo: baseGpsInfo,
        wifiInfo: baseWifiInfo
      };

    case MOCK_SCENARIOS.NO_GPS:
      // 无GPS权限
      return {
        attendanceRules: {
          ...baseAttendanceRules,
          clockType: 1, // 限制位置
          clockLocationList: [{
            gpsFlag: 1, // 需要GPS验证
            wifiFlag: 0,
            gpsList: [{ latitude: '39.9042', longitude: '116.4074', range: 100 }],
            wifiList: []
          }]
        },
        currentSignInfo: baseCurrentSignInfo,
        gpsInfo: { latitude: null, longitude: null }, // 无GPS权限
        wifiInfo: baseWifiInfo
      };

    case MOCK_SCENARIOS.OUT_RANGE:
      // 不在打卡范围内
      return {
        attendanceRules: {
          ...baseAttendanceRules,
          clockType: 1,
          clockLocationList: [{
            gpsFlag: 1,
            wifiFlag: 0,
            gpsList: [{ latitude: '39.9042', longitude: '116.4074', range: 50 }], // 50米范围
            wifiList: []
          }]
        },
        currentSignInfo: {
          ...baseCurrentSignInfo,
          clockInTime: `${currentDate} 09:00:00`,
          onStatus: 0, // 正常上班
        },
        gpsInfo: { latitude: '31.2304', longitude: '121.4737' }, // 上海位置，距离很远
        wifiInfo: baseWifiInfo
      };

    case MOCK_SCENARIOS.IN_RANGE:
      // 在打卡范围内
      return {
        attendanceRules: {
          ...baseAttendanceRules,
          clockType: 1,
          clockLocationList: [{
            gpsFlag: 1,
            wifiFlag: 0,
            gpsList: [{ latitude: '39.9042', longitude: '116.4074', range: 1000 }], // 1000米范围
            wifiList: []
          }]
        },
        currentSignInfo: baseCurrentSignInfo,
        gpsInfo: baseGpsInfo, // 在办公室位置
        wifiInfo: baseWifiInfo
      };

    case MOCK_SCENARIOS.LATE_CLOCK_IN:
      // 迟到打卡
      return {
        attendanceRules: baseAttendanceRules,
        currentSignInfo: {
          ...baseCurrentSignInfo,
          clockInTime: `${currentDate} 09:30:00`, // 迟到30分钟
          onStatus: 1 // 迟到状态
        },
        gpsInfo: baseGpsInfo,
        wifiInfo: baseWifiInfo
      };

    case MOCK_SCENARIOS.EARLY_CLOCK_OUT:
      // 早退打卡
      return {
        attendanceRules: baseAttendanceRules,
        currentSignInfo: {
          ...baseCurrentSignInfo,
          clockInTime: `${currentDate} 09:00:00`,
          clockOutTime: `${currentDate} 17:00:00`, // 早退1小时
          onStatus: 0, // 正常上班
          offStatus: 2 // 早退状态
        },
        gpsInfo: baseGpsInfo,
        wifiInfo: baseWifiInfo
      };

    case MOCK_SCENARIOS.BOTH_CLOCKED:
      // 已完成上下班打卡
      return {
        attendanceRules: baseAttendanceRules,
        currentSignInfo: {
          ...baseCurrentSignInfo,
          clockInTime: `${currentDate} 09:00:00`,
          clockOutTime: `${currentDate} 18:00:00`,
          onStatus: 0, // 正常上班
          offStatus: 0 // 正常下班
        },
        gpsInfo: baseGpsInfo,
        wifiInfo: baseWifiInfo
      };

    case MOCK_SCENARIOS.NO_CLOCK:
      // 未打卡
      return {
        attendanceRules: baseAttendanceRules,
        currentSignInfo: baseCurrentSignInfo,
        gpsInfo: baseGpsInfo,
        wifiInfo: baseWifiInfo
      };

    case MOCK_SCENARIOS.FORGOT_CLOCK_IN:
      // 忘记打上班卡
      return {
        attendanceRules: baseAttendanceRules,
        currentSignInfo: {
          ...baseCurrentSignInfo,
          onStatus: 3 // 忘记打卡状态
        },
        gpsInfo: baseGpsInfo,
        wifiInfo: baseWifiInfo
      };

    case MOCK_SCENARIOS.HOLIDAY:
      // 假期模式
      return {
        attendanceRules: {
          ...baseAttendanceRules,
          workFlag: 0 // 假期，不需要工作
        },
        currentSignInfo: {
          ...baseCurrentSignInfo,
          workFlag: 0
        },
        gpsInfo: baseGpsInfo,
        wifiInfo: baseWifiInfo
      };

    case MOCK_SCENARIOS.NORMAL:
    default:
      // 正常状态
      return {
        attendanceRules: baseAttendanceRules,
        currentSignInfo: baseCurrentSignInfo,
        gpsInfo: baseGpsInfo,
        wifiInfo: baseWifiInfo
      };
  }
}

/**
 * 应用Mock数据到组件实例
 * @param {Object} vm - Vue组件实例
 * @param {string} scenario - 场景枚举值
 * @param {string} currentDate - 当前日期
 */
export function applyMockData(vm, scenario, currentDate) {
  if (!vm.mockEnabled) return;

  console.log(`🧪 应用Mock数据场景: ${scenario}`);

  const mockData = getMockData(scenario, currentDate);

  // 应用Mock数据到组件
  vm.SET_ATTENDANCE_RULES(mockData.attendanceRules);
  vm.SET_CURRENT_SIGN_INFO(mockData.currentSignInfo);
  vm.gpsInfo = mockData.gpsInfo;
  vm.wifiInfo = mockData.wifiInfo;

  console.log('🧪 Mock数据已应用:', {
    scenario,
    attendanceRules: mockData.attendanceRules.attendanceName,
    clockType: mockData.attendanceRules.clockType,
    hasGps: !!mockData.gpsInfo.latitude,
    clockInTime: mockData.currentSignInfo.clockInTime,
    clockOutTime: mockData.currentSignInfo.clockOutTime
  });
}
