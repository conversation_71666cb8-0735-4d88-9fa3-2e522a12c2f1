import Compressor from 'compressorjs';
import { i18n } from '@/i18n';
/**
 * 图片压缩工具函数
 * @param {File} file - 原始文件
 * @param {Object} options - 压缩选项
 * @param {number} options.maxWidth - 最大宽度
 * @param {number} options.maxHeight - 最大高度
 * @param {number} options.quality - 压缩质量
 * @returns {Promise<File>} - 返回压缩后的文件
 */
export function compressImage(file, options = {}) {
  return new Promise((resolve, reject) => {
    if (!file || !file.type.includes('image')) {
      reject(new Error(i18n.t('identityInfo.pleaseUploadImage')));
      return;
    }

    // eslint-disable-next-line no-new
    new Compressor(file, {
      quality: options.quality || 0.5,
      maxWidth: options.maxWidth || 1920,
      maxHeight: options.maxHeight || 1920,
      // 保持原始图片格式
      mimeType: file.type,
      // 压缩成功回调
      success(result) {
        console.log('压缩后的文件大小:', result.size);
        // 检查文件大小
        if (result.size >= 10485760) { // 10MB
          reject(new Error(i18n.t('identityInfo.fileSizeCannotExceed', { size: 10 })));
          return;
        }
        // 返回压缩后的文件
        resolve(new File([result], file.name, { type: file.type }));
      },
      // 压缩失败回调
      error(err) {
        reject(new Error(i18n.t('identityInfo.compressImageError', { error: err.message })));
      }
    });
  });
}
