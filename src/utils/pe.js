// 秘鲁千分位用点.   小数点用逗号 ,   且保留两位小数
export const formatCurrencyPeruCustom = (amount) => {
  if (!amount || amount === '0') {
    return 0
  }
  if (typeof amount === 'string') {
    amount = amount.replace(/,/g, '')
  }
  // 首先，将金额转换为保留两位小数的字符串。toFixed(2) 用于将数字格式化为保留两位小数的字符串。
  const amountStr = Number(amount).toFixed(2);
    
  // 然后，将小数点替换为逗号
  const withoutDecimalPoint = amountStr.replace(".", ",");
  
  // 接着，反转字符串，每三位添加一个点，然后再次反转回原始顺序
  const reversed = withoutDecimalPoint.split("").reverse().join("");
  const withCommas = reversed.replace(/(\d{3})(?=\d)/g, "$1.");
  
  // 最后，再次反转字符串以获得正确的顺序，并返回结果
  return withCommas.split("").reverse().join("");
}
// 秘鲁，千分位用点.   小数点用逗号 ,   去掉小数位显示
export const formatPeruCustomInt = (amount) => {
  if (!amount || amount === '0') {
    return 0
  }
  if (typeof amount === 'string') {
    amount = amount.replace(/,/g, '')
  }
  // 首先，将金额转换为保留两位小数的字符串。toFixed(2) 用于将数字格式化为保留两位小数的字符串。
  const amountStr = Number(amount).toFixed(2);
    
  // 然后，将小数点替换为逗号
  const withoutDecimalPoint = amountStr.replace(".", ",");
  
  // 接着，反转字符串，每三位添加一个点，然后再次反转回原始顺序
  const reversed = withoutDecimalPoint.split("").reverse().join("");
  const withCommas = reversed.replace(/(\d{3})(?=\d)/g, "$1.");
  
  // 最后，再次反转字符串以获得正确的顺序，并返回结果
  const result = withCommas.split("").reverse().join("");

  return result.split(',')[0]
}

// 获取Number类型，用于计算
export const getOriNumberPe = (amount) => {
  //  例如 1.500,22
  if (!amount || amount === '0') {
    return 0
  }
  if (typeof amount === 'string') {
    let realAmt = 0
    amount = amount.replace(/\./g, '')
    amount = amount.replace(/,/, '.')
    realAmt = amount
    return Number(realAmt);
  } else {
    let realAmt = 0
    amount = amount + ''
    realAmt = amount.replace(/\./g, '')
    return Number(realAmt)
  }
}