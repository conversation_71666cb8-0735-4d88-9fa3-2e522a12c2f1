import Big from 'big.js';
import { distance } from "@turf/distance";
import { point } from "@turf/helpers";

/**
 * 提供全局方法
 *
 * **/
"use strict";
/**
 * Base64 解加密
 *
 * @param  { String } stringToEncode/encodedData
 * @return { String } encodedData/stringDecode
 * **/

/**
 * 判断为空
 * **/

/**
 * 变量
 *
 * @param  { var } key
 * @return { Boolean }
 * **/
export const isEmpty = key =>
  !key || typeof key === "undefined" || key === null;

/**
 * 数组
 *
 * @param  { Array<Object> } arr
 * @return { Boolean }
 * **/
export const isEmptyArray = arr => !arr.length;

/**
 * 对象
 *
 * @param  { Object } obj
 * @return { Boolean }
 * **/
export const isEmptyObject = obj => {
  if (obj === null) {
    return true
  } else if (typeof obj === "object" && !(obj instanceof Array) && obj !== null) {
    for (const t in obj) return !1;
    return !0;
  }
};

/**
 * 路由
 * **/

/**
 * 将参数部分（search）转为对象
 *
 * @return { * }
 * **/
export const getUrlParam = s => {
  const urlParamObj = {};
    let search = s || location.search;

  if (search.indexOf("?") > -1) {
    search = search.substr(1);
    const searchArr = search.split("&");

    for (let i = 0, len = searchArr.length; i < len; i++) {
      urlParamObj[searchArr[i].split("=")[0]] = searchArr[i].split(
        "="
      )[1];
    }
  }

  return urlParamObj;
};

/**
 * 计算数字
 *
 * **/

/** 对比数字
 *@param {string} cVersion 需要对比的版本号
 *@param {string} bVersion 版本号界线
 * return {number} 小于返回-1;等于返回0;大于返回1
 * **/
export const getVersionCompared = (cVersion, bVersion) => {
  const cVersionArr = cVersion.split(".");
    const bVersionArr = bVersion.split(".");
  const length = Math.max(cVersionArr.length, bVersionArr.length);
  for (let i = 0; i < length; i++) {
    if (typeof cVersionArr[i] === "undefined") cVersionArr.push("0");
    if (typeof bVersionArr[i] === "undefined") bVersionArr.push("0");
    if (Number(cVersionArr[i]) < Number(bVersionArr[i])) {
      return -1;
    } else if (Number(cVersionArr[i]) > Number(bVersionArr[i])) {
      return 1;
    }
  }
  return 0;
};

/**
 * 四舍五入
 *
 * @param { Number } number
 * @param { Number } digit
 * **/
export const getMathRound = (number = 0, digit = 2) => {
  const exponent = Math.pow(10, digit);
  return Math.round(number * exponent) / exponent;
};


/**
 * 浏览器 browser
 * 内核 kernel & 版本 version
 *
 * @return {*}  kernel, version
 * **/
export const getBrowser = () => {
  let s;
    let kernel = "";
    let version = "";
    const ua = navigator.userAgent.toLowerCase();

  //探测浏览器
  // eslint-disable-next-line
  (s = ua.match(/msie ([\d.]+)/))
    ? _set("ie", _toFixedVersion(s[1]))
  // eslint-disable-next-line
    : (s = ua.match(/firefox\/([\d.]+)/))
      ? _set("firefox", _toFixedVersion(s[1]))
      // eslint-disable-next-line
      : (s = ua.match(/chrome\/([\d.]+)/))
        ? _set("chrome", _toFixedVersion(s[1]))
        // eslint-disable-next-line
        : (s = ua.match(/opera.([\d.]+)/))
          ? _set("opera", _toFixedVersion(s[1]))
          // eslint-disable-next-line
          : (s = ua.match(/version\/([\d.]+).*safari/))
            ? _set("safari", _toFixedVersion(s[1]))
            : 0;

  function _toFixedVersion(ver, floatLength) {
    ver = ("" + ver).replace(/_/g, ".");
    floatLength = floatLength || 1;
    ver = String(ver).split(".");
    ver = ver[0] + "." + (ver[1] || "0");
    ver = Number(ver).toFixed(floatLength);
    return ver;
  }

  function _set(k, v) {
    kernel = k;
    version = v;
  }

  return {
    kernel,
    version
  };
};

/**
 * 正则替换/匹配
 * **/

/**
 * 移除所有空格
 *
 * */
export const removeAllSpace = v => v.replace(/\s+/g, "");

// 移除所有超链接
export const removeAllHyperLink = html => {
  const reg = new RegExp(/<a[^>]*href=['"]([^"]*)['"].*?[^>]*>(.*?)<\/a>/gi);
  return reg.test(html) ? html.replace(reg, "") : html;
};

// 替换所有超链接为文本
export const replaceAllHyperLinkToText = html => {
  const reg = new RegExp(/<a[^>]*href=['"]([^"]*)['"].*?[^>]*>(.*?)<\/a>/gi);
  const regAnchoStart = new RegExp(/<a[^>]*href=['"]([^"]*)['"].*?[^>]*>/gi);
  const regAnchoEnd = new RegExp(/<\/a>/gi);
  return reg.test(html)
    ? html.replace(regAnchoStart, "").replace(regAnchoEnd, "")
    : html;
};

/**
 * 检测空白行
 *
 * 匹配任意空或空白字符，如果你什么也没输入，或输入的只有空格、回车、换行等字符，则匹配成功
 */
export const isBlank = str => {
  const reg = /^\s*$/;
  return reg.test(str);
};

/**
 * 验证URL地址
 *
 * **/
export const checkUrl = url => {
  const re = new RegExp(
    "(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/"
  );
  return re.test(url);
};

/**
 * 验证手机号
 *
 * @param  { String } phone
 * @return { Boolean }
 * **/
export const checkPhone = phone =>
  /^1[34578]\d{9}$/.test(phone) || /^8\d+$/.test(phone);

/**
 * 验证账号
 * 由6-20位的字母 或者数字 或者字母加数字
 *
 * @param  { String } account
 * @return { Boolean }
 * **/
export const checkAccount = account => /[0-9A-Za-z]{6,20}$/.test(account);

/**
 * 验证密码
 */
export const checkPassword = password => /^(?=.*\d+)(?=.*[a-zA-Z]).{8,20}$/.test(password)

/**
 * 清除 字符 前后空格
 *
 * @param  { String } str
 * @return { String }
 * **/
export const clearSpace = str => str.replace(/(^\s*)|(\s*)$/g, "");

/**
 * 替换文本中所有换行(\n)为<br/>
 *
 * @param  { String } str
 * @return { String }
 * **/
export const replaceAllLineBreakWithBr = str => {
  const reg = new RegExp(/\\+n/g);
  return reg.test(str) ? str.replace(reg, "<br/>") : str;
};

/**
 * 匹配html(以 p | div | a | span为例)
 * **/
export const resExpHtml = str => {
  const reg = new RegExp(
    /(<p|a|div|span).*(?=>)(.|\n)*?<\\?\/(p|a|div|span)>/g
  );
  return reg.test(str);
};

/**
 * 清除 内容中 \n | \\n
 * * **/
export const removeAllLineBreak = str => {
  const reg = new RegExp(/\\+n/g);
  return reg.test(str) ? str.replace(reg, "") : str;
};

/**
 * 去除字符串中 <br/>
 *
 * @param { String } str
 * **/
export const removeBr = str => {
  const reg = new RegExp(/<br\s*\\?\/?>/gi);
  return reg.test(str) ? str.replace(reg, "") : str;
};

/**
 * 判断数据类型
 *
 * @param  arr/obj/fn
 * @return { Boolean }
 * **/
export const isArray = arr =>
  Object.prototype.toString.call(arr) === "[object Array]";
export const isObject = obj =>
  Object.prototype.toString.call(obj) === "[object Object]";
export const isFun = fn =>
  Object.prototype.toString.call(fn) === "[object Function]";

/**
 * 时间格式化
 * **/
export const dateFormat = (date, fmt) => {
  const o = {
    "M+": date.getMonth() + 1, //月份
    "d+": date.getDate(), //日
    "h+": date.getHours() % 24 === 0 ? "00" : date.getHours() % 24,
    "H+": date.getHours(), //小时
    "m+": date.getMinutes(), //分
    "s+": date.getSeconds(), //秒
    "q+": Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds() //毫秒
  };

  const week = {
    0: "/u65e5",
    1: "/u4e00",
    2: "/u4e8c",
    3: "/u4e09",
    4: "/u56db",
    5: "/u4e94",
    6: "/u516d"
  };

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (RegExp.$1.length > 1
        ? RegExp.$1.length > 2
          ? "/u661f/u671f"
          : "/u5468"
        : "") + week[date.getDay() + ""]
    );
  }

  for (var k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1
          ? o[k]
          : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }

  return fmt;
};
/** 时间显示日月年**/
export const dateFormatDateMonthYear = date => {
  return dateFormat(new Date(Number(date)), "yyyy-MM-dd");
};

/** 时间显示日月年**/
export const dateFormatDateMonthYearI = date => {
  return dateFormat(new Date(Number(date)), "dd/MM/yyyy");
};

/** 时间显示日月年**/
export const dateAddDays = (date, number) => {
  return addDays(new Date(Number(date)), number);
};

export const addDays = (date, days) => {
  let nd = new Date(date);
  nd = nd.valueOf();
  nd = nd + days * 24 * 60 * 60 * 1000;
  nd = new Date(nd);
  const y = nd.getFullYear();
  let m = nd.getMonth() + 1;
  let d = nd.getDate();
  if (m <= 9) m = "0"+m;
  if (d <= 9) d = "0"+d;
  return d + "/" + m + "/" + y;
}

/** 时间显示年月日**/
export const dateFormatYearMonthDate = date => {
  return dateFormat(new Date(Number(date)), "yyyy-MM-dd");
};


/** 时间显示月日时间**/
export const dateFormatMonthDateTime = date => {
  return dateFormat(new Date(Number(date)), "MM-dd HH:mm");
};

/** 时间显示月日时间**/
export const dateFormatYearMonthDateTime = date => {
  return dateFormat(new Date(Number(date)), "yyyy-MM-dd HH:mm");
};

/** 时间显示年月日时分秒 */
export const dateFormatDateTime = date => {
  return dateFormat(new Date(Number(date)), "yyyy-MM-dd HH:mm:ss");
};

/** 时间显示月日 **/
export const dateFormatMonthDate = date => {
  return dateFormat(new Date(Number(date)), "MM月dd日");
};
/** 时间显示月日 **/
export const dateFormatMonthDateX = date => {
  return dateFormat(new Date(Number(date)), "MMyy");
};

/** 时间显示时间 **/
export const dateFormatTime = date => {
  return dateFormat(new Date(Number(date)), "HH:mm:ss");
};

/**
 * 根据key值对应的value值,放在新的数组里
 * @param k = 要匹配的key值
 * @param arr = 要进行筛选匹配的数据源
 * @returns {*}
 */
export const returnValueAccordingKey = (k, arr) => {
  const keyValueArr = [];
  arr.forEach(item => {
    for (const o in item) {
      o === k && keyValueArr.push(item[o]);
    }
  });
  return keyValueArr;
};

/**
 * 根据所需的属性值匹配数组内对应对象
 * @param k = 要匹配的参数
 * @param i = 与之匹配的值
 * @param arr = 检索的数组
 * **/
export const returnObjAccordingKey = (k, i, arr) => {
  const keyObj = arr.find(item => {
    return item[k] === i;
  });
  return keyObj;
};

/**
 * 初始化值为单位亿
 * */
export const toBillion = value => {
  value = (Math.round(parseFloat(value) / 1000000) / 100).toFixed(2);
  return toThousand(value);
}

/**
 * 初始化浮点数
 * */
export const toDouble = (value, { type = "round", decimal = 2 } = {}) => {
  value = parseFloat(value);

  // 空值直接返回默认横杆
  if (isNaN(value)) {
    return "--";
  }

  switch (type) {
    case "round":
      value = (
        Math.round(value * Math.pow(10, decimal)) /
        Math.pow(10, decimal)
      ).toFixed(decimal); // 补全小数位数
      break;
    case "floor":
      value = (
        Math.floor(value * Math.pow(10, decimal)) /
        Math.pow(10, decimal)
      ).toFixed(decimal); // 补全小数位数
      break;
    case "ceil":
      value = (
        Math.floor(value * Math.pow(10, decimal)) /
        Math.pow(10, decimal)
      ).toFixed(decimal); // 补全小数位数
      break;
  }

  return toThousand(value);
}

/**
 * 倒计时计算年，天，时，分，秒
 * @param targetTime {Number/String} 目标时间（将来的设定时间）
 * @param startTime {Number/String} 开始时间（一般为现在时间）
 * @return {{years: *, days: *, hours: *, minutes: *, seconds: *}} 返回对象
 */
export const returnLeftTime = (targetTime, startTime) => {
  // 不足2位数补0方法
  function checkTime(i) {
    if (i < 10) {
      i = "0" + i;
    }
    return String(i);
  }

  const currTime = startTime || new Date().getTime();
  const leftTime = Number(targetTime) - Number(currTime);
  if (leftTime >= 0) {
    const years = parseInt(leftTime / 1000 / 60 / 60 / 24 / 365, 10); ////计算剩余的年
    const days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数
    const hours = parseInt((leftTime / 1000 / 60 / 60) % 24, 10); //计算剩余的小时
    const minutes = parseInt((leftTime / 1000 / 60) % 60, 10); //计算剩余的分钟
    const seconds = parseInt((leftTime / 1000) % 60, 10); //计算剩余的秒数

    return {
      years: checkTime(years),
      days: checkTime(days),
      hours: checkTime(hours),
      minutes: checkTime(minutes),
      seconds: checkTime(seconds)
    };
  }
};

/**
 * 保存Cookie
 * @param key {String} 键
 * @param value {String} 值
 * @param expire {String/Number} 失效时间(单位小时)
 */
export const setCookie = (key, value, expire) => {
  const oDate = new Date();
  oDate.setHours(oDate.getHours() + expire);
  document.cookie =
    key +
    "=" +
    value +
    (expire === null ? "" : ";expires=" + oDate.toGMTString()) +
    ";path=/";
};

/**
 * 移除Cookie
 * @param key {String} 键
 */
export const removeCookie = key => {
  //这里只需要把Cookie保质期退回一天便可以删除
  const myDate = new Date();
  myDate.setTime(-1000); //设置时间
  const cookiesArray = document.cookie.split(";");
  for (let i = 0; i < cookiesArray.length; i++) {
    const varName = cookiesArray[i].split("=");
    /* 清除cookie BUG， document.cookie 取到的结果大于两个字段时，会在字段前面加一个 空格，所以，在做匹配时，需要手动去除前面空格 */
    if (varName[0].trim() === key) {
      document.cookie =
        varName[0] + "='';expires=" + myDate.toGMTString() + ";path=/";
    }
  }
};

/**
 * 获取Cookie
 * @param key {String} 键
 * @return {*}
 */
export const getCookie = key => {
  const cookieArr = document.cookie.split("; ");
  for (let i = 0; i < cookieArr.length; i++) {
    const arr = cookieArr[i].split("=");
    if (arr[0] === key) {
      return arr[1];
    }
  }
  return false;
};

/**
 * 两个数组合并一个对象
 * @param arr1 {Array} 被合并数组，在对象中以 键 存在
 * @param arr2 {Array} 被合并数组，在对象中以 值 存在
 * @return {Object} 返回键值对的对象
 */
export const arrContactObject = (arr1, arr2) => {
  const obj = {};
  for (let i = 0; i < arr1.length; i++) {
    obj[arr1[i]] = arr2[i];
  }
  return obj;
};

/**
 * 强制保留n位小数
 * @param value
 * @param n
 * @return {number}
 */
export const returnFloat = (value, n) => {
  let v = Math.round(parseFloat(value) * 100) / 100;
  const xsd = v.toString().split(".");
  if (xsd.length === 1) {
    v = v.toString() + "." + "0".repeat(n);
    if (v === '0.') v = 0
    return v;
  }
  if (xsd.length > 1) {
    if (xsd[1].length < n) {
      v = v.toString() + "0";
    }
    return v;
  }
};

export const changeTitle = title => {
  document.title = title;
  const i = document.createElement("iframe");
  i.style.display = "none";
  i.src = "https://resource.comein.cn/comein-files/img/default/favicon.ico";
  i.onload = () => {
    setTimeout(() => {
      i.remove();
    }, 0);
  };
  document.body.appendChild(i);
};

/**
 * 获取当前日期的N个月前后的日期
 * by ziJun
 * @param months {number} N月，+N 表示N月之后; -N 表示N月之前
 * @param days {number} N日， +N 表示N天之后; -N 表示N天之前
 * @param isZero {boolean} 是否需要补零 true(默认) 需要;
 * @param joinSign {string} 日期连接符号 ‘-’(默认)
 * @return {string} 输出需求格式
 */
export const getFewMonthAgoOrAfterDate = (
  months = 0,
  days = 0,
  isZero = true,
  joinSign = "-"
) => {
  const dt = new Date();
  dt.setMonth(dt.getMonth() + months, dt.getDate() + days);
  const Year = dt.getFullYear();
  let Month = dt.getMonth() + 1;
  let Day = dt.getDate();

  // 是否补零
  if (isZero) {
    Month = Month < 10 ? "0" + Month : Month;
    Day = Day < 10 ? "0" + Day : Day;
  }

  // 日期连接符号
  return `${Year}${joinSign}${Month}${joinSign}${Day}`;
};

/**
 * 防抖、 delay时间内只能触发一次、
 */
export const debounce = (fn, delay = 1000, start = true) => {
  let done = true;
  let timer = null;
  return function() {
    const _this = this;
    const args = arguments;

    // 首次执行
    if (start) {
      // 开关打开时，执行任务
      if (done) {
        done = false;
        fn.apply(_this, args);
      }
      // 清空上一次操作
      clearTimeout(timer);
      // 任务开关打开
      timer = setTimeout(function() {
        done = true;
      }, delay);
    } else {
      // 清空上一次操作
      clearTimeout(timer);
      // delay时间之后，任务执行
      timer = setTimeout(function() {
        fn.apply(_this, args);
      }, delay);
    }
  };
};

/**
 * 为图表显示 初始化日期列表
 */
export const getDateListForEchart = (startTime, endTime, separator = "-") => {
  let from = +new Date(startTime);
  const to = +new Date(endTime);

  const arrDate = [];

  while (from < to) {
    const date = new Date(from);
    const year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();

    month = month < 10 ? "0" + month : month;
    day = day < 10 ? "0" + day : day;

    arrDate.push(year + separator + month + separator + day);

    from += 24 * 60 * 60 * 1000;
  }

  return arrDate;
};

/**
 * 数字转为千数位格式
 * 返回格式: 1,111
 * */
export const toThousand = num => {
  return num.toString().replace(/\d+/, function(n) {
    // 先提取整数部分
    return n.replace(/(\d)(?=(\d{3})+$)/g, function($1) {
      // 对整数部分添加分隔符
      return $1 + ",";
    });
  });
};
/**
 * 获取排序过滤条件
 */
export const getSortFilter = (tableSortCode, prop, order) => {
  // 由后端控制默认排序，第一次查询前端只做样式展示，不传排序参数
  if (!order || !tableSortCode) {
    return [];
  }

  const orderCode = order === "ascending" ? "1" : 2;
  const sortCode = tableSortCode[prop];

  return sortCode ? [orderCode + sortCode] : [];
};

/**
 * 获取时间戳范围
 * */
export const getTimeStamp = type => {
  let startTime, endTime;
  const date = new Date();

  switch (type) {
    case "month":
      //初始化日历 默认值为当月
      startTime = new Date(
        date.getFullYear(),
        date.getMonth(),
        1
      ).getTime();
      endTime = new Date(
        date.getFullYear(),
        date.getMonth() + 1,
        0,
        23,
        59,
        59
      ).getTime();

      return [startTime, endTime];
    case "year":
      //初始化日历 默认值为当年
      startTime = new Date(date.getFullYear(), 0, 1).getTime();
      endTime = new Date(
        date.getFullYear() + 1,
        0,
        0,
        23,
        59,
        59
      ).getTime();

      return [startTime, endTime];
    case "lastYear":
      // 最近一年
      startTime = new Date(
        date.getFullYear() - 1,
        date.getMonth(),
        date.getDate() + 1
      ).getTime();
      endTime = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59
      ).getTime();

      return [startTime, endTime];
    case "lastYearOneMore":
      // 最近一年加一个月
      // 产品要求，购买的第三方数据，在买方趋势那里会因为新的月份刚开始还没有数据，所以会出现缺字段问题，所以需要再往前移一个月
      startTime = new Date(
        date.getFullYear() - 1,
        date.getMonth() - 1,
        date.getDate() + 1
      ).getTime();
      endTime = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59
      ).getTime();

      return [startTime, endTime];
    case "lastFifteen":
      //过去十五天
      startTime = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate() - 14
      ).getTime();
      endTime = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59
      ).getTime();

      return [startTime, endTime];
    case "lastThirty":
      //过去三十天
      startTime = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate() - 29
      ).getTime();
      endTime = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59
      ).getTime();

      return [startTime, endTime];
    case "lastEighty":
      // 过去180天
      startTime = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate() - 179
      ).getTime();
      endTime = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59
      ).getTime();

      return [startTime, endTime];
  }
};

/**
 * 图表Echarts grid左右left/right距离，动态调整数字的宽度
 */
export const getGridPaddingByNumber = (padding = 0, len = 1) => {
  const data = {
    1: 18,
    2: 22,
    3: 30,
    4: 40,
    5: 46,
    6: 54,
    7: 64,
    8: 72,
    9: 80,
    10: 88,
    11: 102
  };
  return (data[len] || 0) + padding;
};
export const getGridPaddingByString = (padding = 0, len = 1) => {
  return 12 * len + 10 + padding;
};

/**
 * 兼容不同浏览器的事件监听
 */
export const addEvent = (() => {
  if (window.addEventListener) {
    return (el, eventName, fn) => {
      el.addEventListener(eventName, fn);
    };
  } else if (window.attachEvent) {
    return (el, eventName, fn) => {
      el.attachEvent("on" + eventName, fn);
    };
  } else {
    return () => {};
  }
})();

/** Echarts最大余额法求百分比
 * @param valueList {Array}	传参数组 如： [13,22,23,43]
 * @param idx {Number}	上述数组的的序列号
 * @param precision {Number} 保留几位小数
 * */
export const getPercentValue = (valueList, idx, precision) => {
  // 判断是否为空
  if (!valueList[idx]) {
    return 0;
  }
  // 求和
  var sum = valueList.reduce(function (acc, val) {
    return acc + (isNaN(val) ? 0 : val);
  }, 0)
  if (sum === 0) {
    return 0;
  }
  // 10的2次幂是100，用于计算精度。
  var digits = Math.pow(10, precision);
  // 扩大比例100，
  var votesPerQuota = valueList.map(function (val) {
    return (isNaN(val) ? 0 : val) / sum * digits * 100;
  })
  // 总数，扩大比例意味的总数要扩大
  var targetSeats = digits * 100;
  // 再向下取值，组成数组
  var seats = votesPerQuota.map(function (votes) {
    return Math.floor(votes);
  })
  // 再新计算合计，用于判断与总数量是否相同，相同则占比会100%
  var currentSum = seats.reduce(function (acc, val) {
    return acc + val;
  }, 0)
  // 余数部分的数组：原先数组减去向下取值的数组，得到余数部分的数组
  var remainder = votesPerQuota.map(function (votes, idx) {
    return votes - seats[idx];
  })
  // 给最大最大的余额加1，凑个占比100%；
  while (currentSum < targetSeats) {
    //  找到下一个最大的余额，给其加1
    var max = Number.NEGATIVE_INFINITY;
    var maxId = null;
    for (var i = 0, len = remainder.length; i < len; ++i) {
      if (remainder[i] > max) {
        max = remainder[i];
        maxId = i;
      }
    }
    // 对最大项余额加1
    ++seats[maxId];
    // 已经增加最大余数加1，则下次判断就可以不需要再判断这个余额数。
    remainder[maxId] = 0;
    // 总的也要加1，为了判断是否总数是否相同，跳出循环。
    ++currentSum;
  }
  // 这时候的seats就会总数占比会100%
  return seats[idx] / digits
}


/** 根据key、value值得到转化成数组显示使用
 * @param object = key-value
 * @param custom=自定义
 * */
export const keyValue = (object, custom = '') => {
  const data = [];
  for (const key in object) {
    // eslint-disable-next-line no-prototype-builtins
    if (object.hasOwnProperty(key)) {
      if (!key && custom) {
        delete object[key]
      } else {
        const element = object[key];
        data.push({
          value: key,
          label: element
        })
      }
    }
  }
  if (custom) {
    data.unshift(data[data.length - 1]);
    data.pop();
  }
  return data
}




/** 处理空值
 * @param obj
 * */
export const removeEmptyField = (obj) => {
  let newObj = {}
  if (typeof obj === 'string') {
    obj = JSON.parse(obj)
  }
  if (obj instanceof Array) {
    newObj = []
  }
  if (obj instanceof Object) {
    for (const attr in obj) {
      // eslint-disable-next-line no-prototype-builtins
      if (obj.hasOwnProperty(attr) && obj[attr] !== '' && obj[attr] !== null && obj[attr] !== undefined) {
        if (obj[attr] instanceof Object) {
          if (JSON.stringify(obj[attr]) === '{}' || JSON.stringify(obj[attr]) === '[]') {
            continue
          }
          newObj[attr] = removeEmptyField(obj[attr])
        } else if (
          typeof obj[attr] === 'string' &&
          ((obj[attr].indexOf('{') > -1 && obj[attr].indexOf('}') > -1) ||
            (obj[attr].indexOf('[') > -1 && obj[attr].indexOf(']') > -1))
        ) {
          try {
            const attrObj = JSON.parse(obj[attr])
            if (attrObj instanceof Object) {
              newObj[attr] = removeEmptyField(attrObj)
            }
          } catch (e) {
            newObj[attr] = obj[attr]
          }
        } else {
          newObj[attr] = obj[attr]
        }
      }
    }
  }
  return newObj
}
/** 处理字段
 * @param obj
 * @param resData
 * */
export const modelParams = (obj, resData) => {
  if (resData) {
    for (const i in obj) {
      obj[i] = resData[i]
    }
  }
  return obj
}

/** 处理data数据
 * @param rows
 * */
export const fieldsItem = (rows) => {
  const data = []
  if (rows && rows.length > 0) {
    rows.forEach(item => {
      const obj = {
        label: item.headIndex || item.name,
        value: item.id,
        children: item.children
      }
      data.push(obj)
      if (item.children && item.children.length > 0) {
        obj.children = fieldsItem(item.children)
      }
    })
  }
  return data
}

export const getQueryString = () => {
  const ref = location.href;
  const obj = {};
  if (ref.indexOf("?") !== -1) {
    const index = ref.indexOf("?");
    const name = ref.substring(index + 1, ref.length)
    const str = name.split("&");
    for (let i = 0; i < str.length; i++) {
      obj[str[i].split("=")[0]] = decodeURI(str[i].split("=")[1])
    }
  }
  return obj
}
export const thousandNumber = (e) => {
  if (!e) {
    return 0
  }
  e = e + '';
  if (e.indexOf('.') > -1) {
    const arr = e.split('.')
    const intpart = arr[0].replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    const floatPart = arr[1]
    return intpart + '.' + floatPart;
  } else {
    e = e.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    return e;
  }
}

export const ceilInt = (num, byte) => {
  const n = Math.pow(10, byte);
  return Math.ceil(num * n) / n;
}

//千分位转换
export const singleToThousands = (num) => {
  const strSplit = String(num).toString().split(".");
  const integer = strSplit[0].split("");
  integer.reverse();
  const decimal = strSplit[1];
  const newInteger = [];
  for (let i = 0; i < integer.length; i++) {
    if (i % 3 === 0 && i !== 0) {
      newInteger.push(",");
    }
    newInteger.push(integer[i]);
  }
  newInteger.reverse();
  let result = newInteger.join("");
  if (decimal) {
    result += `.${decimal}`;
  }
  return result;
}

//千分位转换同时保留两位小数
export const thousandsToFixed = (num) => {
  if (typeof num === 'string') {
    num = num.replace(/,/g, '')
  }
  return Number(num).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
}

//千分位转换，0不保留两位小数
export const thousandsToFloat = (num) => {
  return parseFloat(num).toString().replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
}

/**
 * 处理小数相加问题
 * @param {Number} num1 数字1
 * @param {Number} num2 数字2
 * @param {Number} floatNum 保留多少位小数
 */
// export const floatAdd = (num1, num2, floatNum) => {
//   return (Number(num1) * floatNum * 10 + Number(num2) * floatNum * 10) / (floatNum * 10);
// }

/**
   * 处理小数相加问题
   * @param {Number} num1 数字1
   * @param {Number} num2 数字2
  */
 export function floatAdd(num1, num2) {
  const x = new Big(num1);
  return x.plus(num2);
}
/**
 * 处理小数减问题
 * @param {Number} num1 数字1
 * @param {Number} num2 数字2
 * @param {Number} floatNum 剩以多少变成整数
*/
  export function floatMinus(num1, num2) {
  const x = new Big(num1);
  return x.minus(num2);
}

// 判断是否是iOS终端
export const isIOS = () => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
}

// 判断是否是ios中非safari浏览器
// 员工APP ios客户端中WebView使用的是WKWebView，非safari浏览器，所以需要做特殊处理
export const isIOSNonSafari = () => {
  // 1. 首先确保是 iOS 设备
  if (!isIOS()) {
    return false;
  }

  // 2. 检查 User-Agent 是否符合 WKWebView 的特征
  const ua = navigator.userAgent;
  const isWebKit = /AppleWebKit/.test(ua);
  const isNotSafari = !/Safari/.test(ua);

  return isWebKit && isNotSafari;
}

// 判断是否是安卓终端
export const isAndroid = () => {
  const u = navigator.userAgent;
  return u.indexOf('Android') > -1 || u.indexOf('Adr') > -1;
}

// 判断是否是移动设备（iOS或安卓）
export const isMobile = () => {
  return isIOS() || isAndroid();
}


/**
 * 合同确认弹框，每个自然日，只弹出一次
 * @param {String} flag 缓存标识
 * return true已经过期
*/
export const checkValidDay = (flag) => {
  // 获取当前日期
  const currentDate = new Date().toLocaleDateString(); // '2024/3/21'
  // 检查本地存储是否有记录
  const isPopupDisplayed = localStorage.getItem(flag);
  // 如果没有记录或上次记录的日期不是当前日期，则显示弹框
  if (!isPopupDisplayed || isPopupDisplayed !== currentDate) {
    // 更新本地存储的日期
    localStorage.setItem(flag, new Date().toLocaleDateString());
    return false
  } else {
    return true
  }
}


/***
 * 月份转英文缩写
 * @description 数组索引是从0开始, 传入getMonth()返回的月份值即可。
 * **/
export const monthToAbbreviation = (monthNum) => {
  var monthAbbreviations = ['Jan.', 'Feb.', 'Mar.', 'Apr.', 'May', 'Jun.', 'Jul.', 'Aug.', 'Sep.', 'Oct.', 'Nov.', 'Dec.'];
  return monthAbbreviations[monthNum];
}


export const cacheJumpPage = (currentPage) => {
  const cachePage = sessionStorage.getItem('cachePageList')
  if (cachePage) {
    const cachePageList = JSON.parse(cachePage)
    if (cachePageList.length > 0) {
      if (!cachePageList.includes(currentPage)) {
        cachePageList.push(currentPage);
      }
    } else {
      cachePageList.push(currentPage);
    }

    sessionStorage.setItem('cachePageList', JSON.stringify(cachePageList))
  } else {
    const cachePageList = []
    cachePageList.push(currentPage);
    sessionStorage.setItem('cachePageList', JSON.stringify(cachePageList))
  }
}

export const getPrevPage = (currentPage) => {
  // const pageList = ['person', 'kyc', 'IDcard', 'face', 'emerContact']
  let prevPage = ''
  // 根据当前页面，去已加载的缓存记录里面查找当前页面的前一个页面
  const cachePageList = sessionStorage.getItem('cachePageList')
  if (cachePageList) {
    const cachePageArr = JSON.parse(cachePageList)
    for (let i = 0; i < cachePageArr.length; i++) {
      if (cachePageArr[i] === currentPage) {
        // 在找到目标元素时，获取前一个元素
        if (i > 0) {
          prevPage = cachePageArr[i - 1];
        }
        break;
      }
    }
  }

  return prevPage
}

export const scrollIntoView = (domid) => {
  const $dom = document.getElementById(domid);
  $dom.scrollIntoView({ behavior: "smooth", block: "center" });
}

// 处理向下取整
export const roundDownAmount = (amountStr) => {
  return Math.floor(parseFloat(amountStr));
}

/**
* 判断是否有过期, 若过期则移除
* @param {String} flag 缓存标识
* @param {Number} time 时间（小时）
*/
export const inTimeAreaCal = (flag, time) => {
  const flagTime = localStorage.getItem(flag);
  const expiredTime = new Date().getTime() - 60 * 60 * 1000 * time;
  // 超出时间后移除
  if (flagTime > 0 && flagTime < expiredTime) {
    localStorage.removeItem(flag);
    return false;
  }
  if (flagTime > 0 && flagTime > expiredTime) {
    return true;
  }
  return false;
 }


 //  反转一个对象的键和值
export const invertObject = function (obj) {
  const inverted = {};
  for (const key in obj) {
    // 检查属性是否是对象自身的属性而不是继承的属性
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      // 将原始对象的键作为值，将原始对象的值作为键
      inverted[obj[key]] = key;
    }
  }
  return inverted;
}

// 递归替换对象或者数组中的对象的key值
export const replaceKeys = function(obj, keyMap) {
  // 一个递归函数来遍历和替换键
  function traverseAndReplace(item) {
    if (Array.isArray(item)) {
      // 如果是数组，递归其每个元素
      return item.map(traverseAndReplace);
    } else if (item !== null && typeof item === 'object') {
      // 如果是对象，遍历其键
      const newItem = {};
      for (const key in item) {
        // 如果映射中有这个键，则使用映射中的键
        const newKey = keyMap[key] || key;
        // 递归处理值
        newItem[newKey] = traverseAndReplace(item[key]);
      }
      return newItem;
    }
    // 如果不是对象或数组，直接返回值
    return item;
  }

  // 使用递归函数处理输入对象
  return traverseAndReplace(obj);
}

export function compareMacAddresses(mac1, mac2) {
  if (!mac1 || !mac2) {
    return false;
  }
  // 统一分隔符为冒号，并转换为大写
  mac1 = mac1.replace(/-/g, ':').toUpperCase();
  mac2 = mac2.replace(/-/g, ':').toUpperCase();

  // 比较前 12 个字符
  return mac1.substring(0, 12) === mac2.substring(0, 12);
}

/**
 * 验证GPS坐标是否有效
 * @param {number} lat - 纬度
 * @param {number} lng - 经度
 * @returns {boolean} 坐标是否有效
 */
export function isValidCoordinate(lat, lng) {
  const latitude = parseFloat(lat);
  const longitude = parseFloat(lng);

  return !isNaN(latitude) &&
         !isNaN(longitude) &&
         latitude >= -90 &&
         latitude <= 90 &&
         longitude >= -180 &&
         longitude <= 180 &&
         !(latitude === 0 && longitude === 0); // 排除(0,0)坐标
}

/**
 * 计算两个GPS坐标点之间的距离
 * @param {number} lat1 - 第一个点的纬度
 * @param {number} lng1 - 第一个点的经度
 * @param {number} lat2 - 第二个点的纬度
 * @param {number} lng2 - 第二个点的经度
 * @returns {number} 距离（单位：米）
 */
export function calculateGpsDistance(lat1, lng1, lat2, lng2) {
  try {
    // 验证坐标有效性
    if (!isValidCoordinate(lat1, lng1) || !isValidCoordinate(lat2, lng2)) {
      console.warn('GPS坐标无效:', { lat1, lng1, lat2, lng2 });
      return Infinity; // 返回无穷大表示距离验证失败
    }

    // 使用 Turf.js 创建地理坐标点
    const point1 = point([parseFloat(lng1), parseFloat(lat1)]);
    const point2 = point([parseFloat(lng2), parseFloat(lat2)]);

    // 直接计算距离（单位：米）
    const distanceInMeters = distance(point1, point2, { units: 'meters' });

    console.log(`GPS距离计算: 当前位置(${lat1}, ${lng1}) -> 目标位置(${lat2}, ${lng2}) = ${distanceInMeters.toFixed(2)}米`);

    return distanceInMeters;
  } catch (error) {
    console.error('GPS距离计算失败:', error);
    return Infinity; // 计算失败时返回无穷大
  }
}
