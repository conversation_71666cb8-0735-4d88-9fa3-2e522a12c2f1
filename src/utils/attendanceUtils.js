import dayjs, { convertAttendanceTime } from "@/utils/dayjs";

/**
 * 考勤相关工具函数
 * 提供统一的考勤状态判断和时间比较功能
 */

/**
 * 时间比较函数
 * @param {string} time1 - 时间字符串 (HH:mm:ss)
 * @param {string} time2 - 时间字符串 (HH:mm:ss)
 * @returns {number} - 返回值 > 0 表示 time1 > time2，< 0 表示 time1 < time2，= 0 表示相等
 */
export function compareTime(time1, time2) {
  // 将时间字符串转换为秒数进行比较
  const timeToSeconds = (timeStr) => {
    const parts = timeStr.split(':').map(Number);
    const hours = parts[0] || 0;
    const minutes = parts[1] || 0;
    const seconds = parts[2] || 0;
    return hours * 3600 + minutes * 60 + seconds;
  };

  const seconds1 = timeToSeconds(time1);
  const seconds2 = timeToSeconds(time2);

  return seconds1 - seconds2;
}

/**
 * 将时间字符串转换为分钟数
 * @param {string} timeStr - 时间字符串 (HH:mm:ss)
 * @returns {number} - 分钟数
 */
export function timeToMinutes(timeStr) {
  if (!timeStr) return 0;
  const parts = timeStr.split(':').map(Number);
  const hours = parts[0] || 0;
  const minutes = parts[1] || 0;
  return hours * 60 + minutes;
}

/**
 * 将分钟数转换为时间字符串
 * @param {number} minutes - 分钟数
 * @returns {string} - 时间字符串 (HH:mm:ss)
 */
export function minutesToTime(minutes) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:00`;
}

/**
 * 计算两个时间之间的分钟差
 * @param {string} startTime - 开始时间 (HH:mm:ss)
 * @param {string} endTime - 结束时间 (HH:mm:ss)
 * @returns {number} - 分钟差
 */
export function getTimeDifferenceInMinutes(startTime, endTime) {
  if (!startTime || !endTime) return 0;

  const startMinutes = timeToMinutes(startTime);
  const endMinutes = timeToMinutes(endTime);

  return endMinutes - startMinutes;
}

/**
 * 给时间字符串添加指定分钟数
 * @param {string} timeStr - 时间字符串 (HH:mm:ss)
 * @param {number} minutesToAdd - 要添加的分钟数
 * @returns {string} - 新的时间字符串 (HH:mm:ss)
 */
export function addMinutesToTime(timeStr, minutesToAdd) {
  if (!timeStr) return '';

  const totalMinutes = timeToMinutes(timeStr) + minutesToAdd;
  return minutesToTime(totalMinutes);
}
