// 引入 Day.js 和必要的插件
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// 扩展 Day.js 功能
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * 格式化日期
 * @param date YYYY-MM-DD
 */
export function formatDate(date, format) {
  if (date === null || date === undefined || !date) return '';
  return dayjs(date).format(format);
}
/**
 * @param time 格式："2023-10-05 14:30:00"
 */
export function toLocalTime(time, format) {
  if (!time) return '';
  // 将字符串解析为 UTC 时间
  const utcDate = dayjs.utc(time);
  // 转换为本地时间
  const localDate = utcDate.local();
  return localDate.format(format);
}

/**
 * 统一处理考勤规则中的UTC时间字段转换
 * @param {string} utcTime - UTC时间字符串（格式：HH:mm:ss）
 * @param {string} fallbackTime - 备用的非UTC时间字符串
 * @param {string} format - 输出格式，默认为 'HH:mm:ss'
 * @returns {string} - 转换后的本地时间字符串
 */
export function convertAttendanceTime(utcTime, fallbackTime, format = 'HH:mm:ss') {
  // 优先使用UTC时间字段
  const timeToUse = utcTime || fallbackTime;
  if (!timeToUse) return '';

  // 如果是UTC时间字段，需要特殊处理
  if (utcTime) {
    // UTC时间字段是 HH:mm:ss 格式，需要转换为完整的UTC日期时间
    // 使用UTC零时区的今天日期作为基准日期
    const todayUTC = dayjs.utc().format('YYYY-MM-DD');
    const fullUtcDateTime = `${todayUTC} ${utcTime}`;

    // 将完整的UTC时间转换为本地时间
    return toLocalTime(fullUtcDateTime, format);
  }

  // 如果是原有字段，直接返回
  return timeToUse;
}

export default dayjs;
