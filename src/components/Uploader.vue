<template>
  <van-uploader :after-read="afterRead" accept="image/*"/>
</template>

<script>
import { Uploader } from "vant";

export default {
  name: "Uploader",
  components: {
    VanUploader: Uploader
  },
  props: {},
  data() {
    return {
      file: null,
      fileList: [
        // Uploader 根据文件后缀来判断是否为图片文件
        // 如果图片 URL 中不包含类型信息，可以添加 isImage 标记来声明
      ],
    };
  },
  methods: {
    afterRead(file) {
      // 此时可以自行将文件上传至服务器
      this.file = file;
      console.log(file);
      this.$emit("complete", file);
    },
  },
};
</script>
