<template>
  <div class="header" :style="{background: headerColor, borderRadius: borderRadius}" :class="[productSource, customStyle]">
    <div v-if="showBackIcon" class="header-back" @click="backRouter">
      <slot name="left">
        <img :src="backurl"  alt="">
      </slot>
    </div>
    <div class="title" :style="{color: titleColor}">{{ pageName }}</div>
    <div class="right">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
import router from '../router';
import { globalConfig } from '@/api/config';
export default {
  props: {
    pageName: {
      type: String,
      default: ''
    },
    headerColor: {
      type: String,
      default: ''
    },
    fromRouteName: {
      type: String,
      default: ''
    },
    borderRadius: {
      type: String,
      default: '0'
    },
    titleColor: {
      type: String,
      default: '#2c2c2c'
    },
    showBackIcon: {
      type: Boolean,
      default: true
    },
    backFun: {
      type: Function,
      default: () => {
        router.back(-1);
      }
    },
    // 当前页面传入 backFun 处理返回
    selfBackFlag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
        backurl: require('@/assets/images/common/back.png'),
        productSource: '',
        customStyle: ''
    }
  },
  created() {},
  mounted(){
    this.productSource = localStorage.getItem('productSource')
    if (globalConfig.pageStyleSetting && globalConfig.pageStyleSetting.publicHeader) {
      const style = globalConfig.pageStyleSetting.publicHeader;
      this.backurl = require(`@/assets/images/${style}/back.png`);
      this.customStyle = style;
    }
    /*window.nativeBackCallback = function() {
      console.log('nativeBackCallback');
      vm.backRouter();
      // 1 原生不处理返回键  0 原生处理返回键
      return 1
    }*/
  },
  methods: {
    backRouter() {
      if (this.selfBackFlag) {
        this.backFun();
      } else {
        this.$router.back();
        /*const vm = this;
        const name = vm.$route.name;
        backHistoryPage(vm, name, this.fromRouteName)*/
      }
    }
  }
}
</script>

<style scoped lang="scss">
.header {
  width: 100%;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  &.common1{
    @include themify() {
      background: themed('background') !important;
      height: 112px;
    }
  }
}
.header-back {
  position: absolute;
  display: inline-block;
  width: 44px;
  height: 44px;
  left: 24px;
  top: 36px;
  @include themify() {
    width: themed("header-img-size");
    height: themed("header-img-size");
  }
}
.title {
  align-self: center;
  font-size: 36px;
  color: #1b3155;
  font-weight: 500;
  @include themify() {
    width: themed("header-title-width");
    text-align: themed("header-title-align");
    color: themed("header-text-color");
    font-weight: themed("header-font-weight");
  }
  justify-content: center;
  .header-back {
    display: inline-block;
    width: 48px;
    height: 48px;
    position: absolute;
    left: 38px;
  }
  .title {
    font-size: 32px;
    font-weight: 400;
    color: #2c2c2c;
  }
  .right{
    position: absolute;
    right: 8px;
    top: 40px;
  }
}
</style>
