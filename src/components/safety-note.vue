<template>
  <div class="flex-row self-stretch safety-note">
    <img
      class="self-start image_5"
      src="../assets/images/entryProcess/safety-icon.png"
    />
    <div class="flex-col flex-1 self-center ml-17">
      <span class="self-start font_4 text_6">{{$t('personInfo.dataSafe')}}</span>
      <div class="flex-col justify-start self-stretch relative mt-15">
        <span class="font_5 text_7">{{$t('personInfo.dataSafe2')}}</span>
      </div>
    </div>
    <img
      class="image_6"
      src="../assets/images/entryProcess/safety-icon2.png"
    />
  </div>
</template>

<script>
export default {
  components: {},
  props: {},
  data() {
    return {};
  },

  methods: {},
};
</script>

<style scoped lang="scss">
.ml-17 {
  margin-left: 17px;
}
.mt-15 {
  margin-top: 15px;
}
.safety-note {
  position: relative;
  margin: 28px 8px 0 6px;
  padding: 19.5px 13px 19px 24px;
  background-color: #d1e7ff;
  border-radius: 16px 16px 0px 16px;
  .image_5 {
    margin-top: 9.5px;
    width: 37px;
    height: 38px;
  }
  .font_4 {
    font-size: 28px;
    font-weight: 700;
  }
  .text_6 {
    color: #0074ff;
    line-height: 21.5px;
  }
  .image_6 {
    position: absolute;
    bottom: 0;
    right: 13px;
    width: 159px;
    height: 39px;
  }
  .font_5 {
    font-size: 24px;
    line-height: 23px;
    font-weight: 700;
    color: #0074ff;
  }
  .text_7 {
    color: #4773b6;
    line-height: 23.5px;
    font-weight: unset;
  }
}
</style>
