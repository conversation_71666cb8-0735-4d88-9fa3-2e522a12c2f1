<template>
  <div class="lang-selector flex-row items-center" @click.stop="toggleOptions">
    <img class="lang-icon" src="@/assets/images/common/lang-icon.png" />
    <span class="lang-text">{{ currentLang }}</span>
    <img class="lang-arrow" src="@/assets/images/common/lang-arrow.png" />
    <!-- 语言选项下拉框 -->
    <div v-show="showOptions" class="lang-options">
      <div v-for="lang in langOptions"
           :key="lang.value"
           class="lang-option"
           :class="{ active: lang.value === selectedLang }"
           @click.stop="changeLang(lang.value)">
        {{ lang.label }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LangSelector',
  data() {
    return {
      showOptions: false,
      selectedLang: localStorage.getItem('lang') || 'en',
      langOptions: [
        { label: 'English', value: 'en' },
        { label: 'Français', value: 'fr' },
        { label: 'Español', value: 'es' },
        { label: 'Kiswahili', value: 'sw' },
        { label: 'Twi', value: 'tw' },
      ]
    }
  },
  computed: {
    currentLang() {
      const lang = this.langOptions.find(l => l.value === this.selectedLang)
      return lang ? lang.label : 'English'
    }
  },
  mounted() {
    document.addEventListener('click', this.hideOptions)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.hideOptions)
  },
  methods: {
    toggleOptions() {
      this.showOptions = !this.showOptions
    },
    hideOptions() {
      this.showOptions = false
    },
    changeLang(lang) {
      this.selectedLang = lang
      this.$i18n.locale = lang
      localStorage.setItem('lang', lang)
      this.hideOptions()
    }
  }
}
</script>

<style scoped lang="scss">
.lang-selector {
  position: relative;
  cursor: pointer;
  margin-left: 20px;

  .lang-icon {
    width: 28px;
    height: 28px;
    margin-right: 8px;
  }

  .lang-text {
    font-size: 28px;
    color: #1b3155;
    margin-right: 8px;
  }

  .lang-arrow {
    width: 32px;
    height: 32px;
  }

  .lang-options {
    position: absolute;
    top: 100%;
    left: 0;
    width: 160px;
    background: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    z-index: 10;
    margin-top: 8px;

    .lang-option {
      padding: 12px 16px;
      font-size: 28px;
      color: #1b3155;

      &:hover {
        background: #F5F7FA;
      }

      &.active {
        color: #0074FF;
      }
    }
  }
}
</style>
