/**
 * 全局组件
 *
 * */
export default Vue => {
	Vue.component('public-footer', () => import('./public-footer.vue'));
	Vue.component('public-header', () => import('./public-header.vue'));
	Vue.component('page-loading', () => import('./page-loading.vue'));
	Vue.component('page-pop', () => import('./page-pop.vue'));
}

/*
const req = require.context('../assets/svg', false, /\.svg$/)
const requireAll = requireContext => requireContext.keys().map(requireContext)
requireAll(req)*/
