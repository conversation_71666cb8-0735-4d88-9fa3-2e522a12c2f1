<template>
  <div class="c-button" @click="action" :class="[className, { disabled }]">
    <span class="button-text">{{ name }}</span>
    <span class="button-icon">
      <slot name="icon" />
    </span>
  </div>
</template>

<script>
export default {
  name: "c-button",
  props: {
    name: String,
    className: [String, Array],
    disabled: Boolean,
  },
  data() {
    return {};
  },

  mounted() {},

  methods: {
    action() {
      this.$emit("click");
    }
  }
};
</script>

<style lang="scss" scoped>
.c-button {
  height: 100px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: 400;
  line-height: normal;
}
.button-icon {
  margin-left: 12px;
}
</style>
