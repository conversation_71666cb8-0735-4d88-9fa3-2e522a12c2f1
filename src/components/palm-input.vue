<template>
  <div :class="[
    type === 'textarea' ? 'palm-textarea' : 'palm-input',
    {
      'is-disabled': inputDisabled,
      'is-exceed': inputExceed,
      'palm-input-group': $slots.prepend || $slots.append || label,
      'palm-input-group--append': $slots.append,
      'palm-input-group--prepend': $slots.prepend,
      'palm-input--prefix': $slots.prefix,
      'palm-input--suffix': $slots.suffix || showPassword
    }
    ]"
  >
    <div class="palm-input-group__label" v-if="label" :class="[(value || focused || $attrs.placeholder) ? 'palm-input-group__label-focus' : '']" :style="$attrs.placeholder && !focused ? 'color: #c4cad5' : ''" @click="focus">
      <span class="label_required">{{ isRequired ? '*': '&nbsp;'}}</span>
      {{label}}
    </div>
    <template v-if="type !== 'textarea'">
      <!-- 前置元素 -->
      <div class="palm-input-group__prepend" v-if="$slots.prepend">
        <slot name="prepend"></slot>
      </div>
      <input
        :tabindex="tabindex"
        v-if="type !== 'textarea'"
        class="palm-input__inner"
        :placeholder="placeholder"
        v-bind="$attrs"
        :type="showPassword ? (passwordVisible ? 'text': 'password') : type"
        :disabled="inputDisabled"
        :readonly="readonly"
        :autocomplete="autocomplete"
        ref="input"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @change="handleChange"
        @paste="handlePaste"
        @keyup="handleKeyup"
        :aria-label="label"
      >
<!--  @compositionstart="handleCompositionStart"
        @compositionupdate="handleCompositionUpdate"
        @compositionend="handleCompositionEnd"    -->
      <!-- 前置内容 -->
      <span class="palm-input__prefix" v-if="$slots.prefix">
        <slot name="prefix"></slot>
      </span>
      <!-- 后置内容 -->
      <span
        class="palm-input__suffix"
        v-if="getSuffixVisible()">
        <span class="palm-input__suffix-inner">
          <template v-if="!showPwdVisible || !isWordLimitVisible">
            <slot name="suffix"></slot>
          </template>
          <i v-if="showPwdVisible"
            class="palm-input__icon palm-icon-view palm-input__clear"
            @click="handlePasswordVisible"
          ></i>
          <span v-if="isWordLimitVisible" class="palm-input__count">
            <span class="palm-input__count-inner" :class="focused ? 'focused' : ''">
              {{ textLength }}/{{ upperLimit }}
            </span>
          </span>
        </span>
      </span>
      <!-- 后置元素 -->
      <div class="palm-input-group__append" v-if="$slots.append">
        <slot name="append"></slot>
      </div>
    </template>
    <textarea
      v-else
      :tabindex="tabindex"
      class="palm-textarea__inner"
      @compositionstart="handleCompositionStart"
      @compositionupdate="handleCompositionUpdate"
      @compositionend="handleCompositionEnd"
      @input="handleInput"
      ref="textarea"
      v-bind="$attrs"
      :disabled="inputDisabled"
      :readonly="readonly"
      :autocomplete="autocomplete"
      :style="textareaStyle"
      @focus="handleFocus"
      @blur="handleBlur"
      @change="handleChange"
      :aria-label="label"
    >
    </textarea>
    <span v-if="isWordLimitVisible && type === 'textarea'" class="palm-input__count" :class="focused ? 'focused' : ''">{{ textLength }}/{{ upperLimit }}</span>
  </div>
</template>

<script>
import calcTextareaHeight from '../utils/calcTextareaHeight';
import { isIOSNonSafari } from "@/utils/tools";
export default {
  name: 'PalmInput',
  componentName: 'PalmInput',
  inheritAttrs: false,
  data() {
    return {
      textareaCalcStyle: {},
      focused: false,
      isComposing: false,
      passwordVisible: false,
      composeTimer: null
    };
  },
  props: {
    value: [String, Number],
    placeholder: {
      type: String,
      default: ''
    },
    resize: String,
    form: String,
    disabled: Boolean,
    readonly: Boolean,
    type: {
      type: String,
      default: 'text'
    },
    autosize: {
      type: [Boolean, Object],
      default: false
    },
    autocomplete: {
      type: String,
      default: 'off'
    },
    label: String,
    showPassword: {
      type: Boolean,
      default: false
    },
    showWordLimit: {
      type: Boolean,
      default: false
    },
    tabindex: String,
    isRequired: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    nativeInputValue() {
      return this.value === null || this.value === undefined ? '' : String(this.value);
    },
    inputDisabled() {
        return this.disabled;
    },
    showPwdVisible() {
      return this.showPassword &&
        !this.inputDisabled &&
        !this.readonly &&
        (!!this.nativeInputValue || this.focused);
    },
    isWordLimitVisible() {
      return this.showWordLimit &&
        this.$attrs.maxlength &&
        (this.type === 'text' || this.type === 'tel' || this.type === 'textarea') &&
        !this.inputDisabled &&
        !this.readonly &&
        !this.showPassword;
    },
    textareaStyle() {
      const merge = function(target) {
        for (let i = 1, j = arguments.length; i < j; i++) {
          const source = arguments[i] || {};
          for (const prop in source) {
            if (Object.prototype.hasOwnProperty.call(source, prop)) {
              const value = source[prop];
              if (value !== undefined) {
                target[prop] = value;
              }
            }
          }
        }

        return target;
      };
      return merge({}, this.textareaCalcStyle, { resize: this.resize });
    },
    upperLimit() {
      return this.$attrs.maxlength;
    },
    textLength() {
      if (typeof this.value === 'number') {
        return String(this.value).length;
      }
      return (this.value || '').length;
    },
    inputExceed() {
      // show exceed style if length of initial value greater then maxlength
      return this.isWordLimitVisible &&
        (this.textLength > this.upperLimit);
    }
  },
  watch: {
    value() {
      this.$nextTick(this.resizeTextarea);
    },
    nativeInputValue() {
      this.setNativeInputValue();
    },
    type() {
      this.$nextTick(() => {
        this.setNativeInputValue();
        this.resizeTextarea();
      });
    }
  },
  methods: {
    focus() {
      this.getInput().focus();
    },
    blur() {
      this.getInput().blur();
    },
    handleBlur(event) {
      this.focused = false;
      this.$emit('blur', event);
    },
    select() {
      this.getInput().select();
    },
    resizeTextarea() {
      if (this.$isServer) return;
      const { autosize, type } = this;
      if (type !== 'textarea') return;
      if (!autosize) {
        this.textareaCalcStyle = {
          minHeight: calcTextareaHeight(this.$refs.textarea).minHeight
        };
        return;
      }
      const minRows = autosize.minRows;
      const maxRows = autosize.maxRows;
      this.textareaCalcStyle = calcTextareaHeight(this.$refs.textarea, minRows, maxRows);
    },
    setNativeInputValue() {
      const input = this.getInput();
      if (!input) return;
      if (input.value === this.nativeInputValue) return;
      input.value = this.nativeInputValue;
    },
    // 滚动到可视区域内
    scrollIntoView() {
      if (this.$attrs.id) {
        const $dom = document.getElementById(this.$attrs.id);
        $dom.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    },
    // 手动计算滚动位置，适配iOS虚拟键盘
    scrollIntoViewCentered() {
      const element = this.getInput();
      if (!element) return;

      // 延迟执行，等待键盘动画和视图调整
      setTimeout(() => {
        // 1. 获取元素相对于视窗的位置信息
        const rect = element.getBoundingClientRect();

        // 2. 获取当前窗口的滚动位置
        const currentScrollY = window.scrollY || document.documentElement.scrollTop;

        // 3. 计算元素的绝对顶部位置
        const elementTop = rect.top + currentScrollY;

        // 4. 获取可见视窗的高度（键盘弹出后，这个值会变小）
        const visibleViewportHeight = window.innerHeight;

        // 5. 计算目标滚动位置：
        //    目标 = 元素绝对顶部 - (可见视窗高度 / 2) + (元素高度 / 2)
        //    这样能让元素的垂直中心对齐视窗的垂直中心
        const targetScrollY = elementTop - (visibleViewportHeight / 2) + (element.offsetHeight / 2);

        // 6. 执行平滑滚动
        window.scrollTo({
          top: targetScrollY,
          behavior: 'smooth'
        });

        console.log(`Scrolling to make element centered. Target Y: ${targetScrollY}`);
      }, 300); // 300ms 的延迟通常足够应对大多数情况
    },
    handleFocus(event) {
      this.focused = true;
      this.$emit('focus', event);
      this.$nextTick(() => {
        if (isIOSNonSafari()) {
          this.scrollIntoViewCentered()
        } else {
          this.scrollIntoView()
        }
      })
    },
    /**
     * 防止handleCompositionEnd意外情况未触发时无法输入
     */
    setComposeTimer() {
      if (this.composeTimer) {
        clearTimeout(this.composeTimer);
      }
      this.composeTimer = setTimeout(() => {
        this.isComposing = false;
      }, 500);
    },
    handleCompositionStart() {
      this.isComposing = true;
      this.setComposeTimer();
    },
    handleCompositionUpdate(event) {
      this.isComposing = true;
      this.setComposeTimer();
    },
    handleCompositionEnd(event) {
      if (this.isComposing) {
        this.isComposing = false;
        this.handleInput(event);
      }
    },
    handleKeyup(event) {
      this.$emit('keyup', event.target.value);
      this.$nextTick(this.setNativeInputValue);
    },
    handleInput(event) {
      // should not emit input during composition
      // see: https://github.com/ElemeFE/element/issues/10516
      if (this.isComposing) return;
      // hack for https://github.com/ElemeFE/element/issues/8548
      // should remove the following line when we don't support IE
      if (event.target.value === this.nativeInputValue) return;
      this.$emit('input', event.target.value);

      // ensure native input value is controlled
      // see: https://github.com/ElemeFE/element/issues/12850
      this.$nextTick(this.setNativeInputValue);
    },
    handleChange(event) {
      this.$emit('change', event.target.value);
    },
    handlePaste(event) {
      this.$emit('paste', event.target.value);
    },
    handlePasswordVisible() {
      this.passwordVisible = !this.passwordVisible;
      this.focus();
    },
    getInput() {
      return this.$refs.input || this.$refs.textarea;
    },
    getSuffixVisible() {
      return this.$slots.suffix ||
        this.showPassword ||
        this.isWordLimitVisible ||
        (this.validateState && this.needStatusIcon);
    }
  },
  created() {
      this.$on('inputSelect', this.select);
    },
  mounted() {
    this.setNativeInputValue();
    this.resizeTextarea();
  }
}
</script>

<style lang="scss" scoped>
.palm-textarea {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: bottom;
  font-size: 32px;
}

.palm-textarea__inner {
  display: block;
  resize: vertical;
  padding: 15px 12px 0 10px;
  line-height: 27px;
  caret-color: #099bfa;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #1b3155;
  background-color: #fff;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
  &::placeholder {
    color: #C4CAD5;
  }
  &:hover {
    // border-color: #C4CAD5;
  }
  &:focus {
    outline: 0;
    border-color: #099bfa;
    background-color: #fff;
  }
}

.palm-textarea .palm-input__count {
  color: #536887;
  background: #f3f5f6;
  position: absolute;
  font-size: 20px;
  bottom: 5px;
  right: 10px;
  &focused {
    background: #fff;
  }
}

.palm-textarea.is-disabled .palm-textarea__inner {
  background-color: #f3f5f6;
  border-color: transparent;
  color: #1b3155;
  cursor: not-allowed;
  &::placeholder {
    color: #C4CAD5;
  }
}

.palm-textarea.is-exceed .palm-textarea__inner {
  border-color: #F56C6C;
  font-size: 32px;
}

.palm-textarea.is-exceed .palm-input__count {
  color: #F56C6C;
  font-size: 32px;
}

.palm-input {
  position: relative;
  font-size: 32px;
  display: inline-block;
  width: 100%;
}

.palm-input::-webkit-scrollbar {
  z-index: 11;
  width: 6px
}

.palm-input::-webkit-scrollbar:horizontal {
  height: 6px
}

.palm-input::-webkit-scrollbar-thumb {
  border-radius: 5px;
  width: 6px;
  background: #b4bccc
}

.palm-input::-webkit-scrollbar-corner {
  background: #fff
}

.palm-input::-webkit-scrollbar-track {
  background: #fff
}

.palm-input::-webkit-scrollbar-track-piece {
  background: #fff;
  width: 6px
}

.palm-input .palm-input__clear {
  color: #C4CAD5;
  font-size: 14px;
  cursor: pointer;
  transition: color .2s cubic-bezier(.645, .045, .355, 1)
}

.palm-input .palm-input__clear:hover {
  color: #909399
}

.palm-input .palm-input__count {
  height: 100%;
  display: inline-flex;
  align-items: center;
  color: #536887;
  font-size: 20px
}

.palm-input .palm-input__count .palm-input__count-inner {
  background: #f3f5f6;
  line-height: initial;
  display: inline-block;
  padding: 0 5px;
  &.focused {
    background: #fff;
  }
}

.palm-input__inner {
  -webkit-appearance: none;
  box-sizing: border-box;
  color: #1b3155;
  display: inline-block;
  font-size: inherit;
  caret-color: #099bfa;
  height: 110px;
  line-height: 30px;
  outline: 0;
  padding: 25px 12px 0 25px;
  transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
  width: 100%;
  background: #fff;
  border: none;
  border-bottom: 1px solid #dbe1ea;
  &::placeholder {
    color: #C4CAD5;
    font-size: 32px;
  }
  &:hover {
    // border-color: #C4CAD5;
  }
}

.palm-input__prefix,
.palm-input__suffix {
  position: absolute;
  top: 0;
  -webkit-transition: all .3s;
  text-align: center;
  height: 100%;
  color: #C4CAD5
}

.palm-input.is-active .palm-input__inner,
.palm-input__inner:focus {
  background-color: #fff;
  outline: 0;
  /*@include themify() {
    border: themed("border");
  }*/
}

.palm-input__suffix {
  right: 5px;
  transition: all .3s;
  z-index: 50;
  pointer-events: none
}

.palm-input__suffix-inner {
  pointer-events: all
}

.palm-input__prefix {
  left: 5px;
  transition: all .3s
}

.palm-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  transition: all .3s;
  line-height: 40px
}

.palm-input__icon:after {
  content: '';
  height: 100%;
  width: 0;
  display: inline-block;
  vertical-align: middle
}

.palm-input__validateIcon {
  pointer-events: none
}

.palm-input.is-disabled .palm-input__inner {
  background-color: #f3f5f6;
  border-color: transparent;
  cursor: not-allowed;
  &::placeholder {
    color: #C4CAD5;
  }
}

.palm-input.is-disabled .palm-input__icon {
  cursor: not-allowed
}

.palm-input.is-exceed .palm-input__inner {
  border-color: #F56C6C;
  font-size: 32px;
}

.palm-input.is-exceed .palm-input__suffix .palm-input__count {
  color: #F56C6C;
  font-size: 32px;
}

.palm-input--suffix .palm-input__inner {
  padding-right: 30px
}

.palm-input--prefix .palm-input__inner {
  padding-left: 30px
}

.palm-input-group {
  line-height: normal;
  display: inline-table;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0
}

.palm-input-group>.palm-input__inner {
  vertical-align: middle;
  display: table-cell
}

.palm-input-group__append,
.palm-input-group__prepend {
  background-color: #F5F7FA;
  color: #909399;
  vertical-align: middle;
  display: table-cell;
  position: relative;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  padding: 0 20px;
  width: 1px;
  white-space: nowrap
}

.palm-input-group--prepend .palm-input__inner,
.palm-input-group__append {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.palm-input-group--append .palm-input__inner,
.palm-input-group__prepend {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.palm-input-group__append:focus,
.palm-input-group__prepend:focus {
  outline: 0
}

.palm-input-group__append .palm-button,
.palm-input-group__append .palm-select,
.palm-input-group__prepend .palm-button,
.palm-input-group__prepend .palm-select {
  display: inline-block;
  margin: -10px -20px
}

.palm-input-group__append button.palm-button,
.palm-input-group__append div.palm-select .palm-input__inner,
.palm-input-group__append div.palm-select:hover .palm-input__inner,
.palm-input-group__prepend button.palm-button,
.palm-input-group__prepend div.palm-select .palm-input__inner,
.palm-input-group__prepend div.palm-select:hover .palm-input__inner {
  border-color: transparent;
  background-color: transparent;
  color: inherit;
  border-top: 0;
  border-bottom: 0
}

.palm-input-group__append .palm-button,
.palm-input-group__append .palm-input,
.palm-input-group__prepend .palm-button,
.palm-input-group__prepend .palm-input {
  font-size: inherit
}

.palm-input-group__prepend {
  border-right: 0
}

.palm-input-group__append {
  border-left: 0
}

.palm-input-group--append .palm-select .palm-input.is-focus .palm-input__inner,
.palm-input-group--prepend .palm-select .palm-input.is-focus .palm-input__inner {
  border-color: transparent
}

.palm-input__inner::-ms-clear {
  display: none;
  width: 0;
  height: 0
}

.palm-input-group__label {
  position: absolute;
  top: calc(50% - 25px);
  left: 24px;
  color: #919DB3;
  line-height: 1.5;
  transition: all .15s ease-in-out;
  pointer-events: none;
  .label_required {
    color: #FF454F;
    font-size: 28px;
    font-style: normal;
    font-weight: 400;
    margin-right: 6px;
  }
  &-focus {
    transform-origin: left;
    left: 24px;
    top: 4px;
    @include themify() {
      color: themed("color");
    }
    font-size: 24px;
    font-weight: 700;
    line-height: normal;
    .label_required {
      position: relative;
      top: 6px;
    }
  }
}
</style>
