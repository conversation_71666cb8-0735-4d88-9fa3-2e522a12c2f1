<template>
  <van-dialog
    className="fix-clock-in-dialog-container"
    :value="value"
    :show-cancel-button="false"
    :show-confirm-button="false"
  >
    <div class="handling-exceptions-card">
      <img src="@/assets/images/common/close1.png" class="close-button" alt="" @click="handleCancel"/>
      <div class="card-header">
        <h2 class="title">{{ $t('attendance.fixClockIn') }}</h2>
      </div>

      <div class="card-body">
        <div class="form-row">
          <label class="form-label">{{ $t('attendance.fixClockInType') }}</label>
          <div class="action-link">
            <span class="clock-type">{{ getClockTypeText }}</span>
            <img src="@/assets/images/common/small_arrow.png" class="arrow" alt=""/>
          </div>
        </div>

        <hr class="separator" />

        <div class="form-row reason-section">
          <label class="form-label">{{ $t('attendance.reason') }}</label>
          <div class="textarea-wrapper">
          <textarea
            v-model="reason"
            :maxlength="200"
            @input="handleReasonInput"
            :placeholder="$t('attendance.reasonPlaceholder')"
          ></textarea>
            <span class="char-counter">{{ reason.length }}/200</span>
          </div>
        </div>
      </div>

      <div class="card-footer">
        <CButton class="confirm-button" :name="$t('attendance.confirm')" v-debounce="handleConfirm" />
      </div>
    </div>
  </van-dialog>
</template>

<script>
import { Dialog } from 'vant';
import CButton from "@/components/c-button.vue";

export default {
  name: 'FixClockInDialog',
  components: {
    CButton,
    [Dialog.Component.name]: Dialog.Component,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    // 补卡类型：'on' 上班卡，'off' 下班卡
    signType: {
      type: String,
      default: 'on',
    },
    // 考勤日期
    attendanceDate: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      reason: '',
    };
  },
  computed: {
    // 获取补卡类型文本
    getClockTypeText() {
      return this.signType === 'on' ? this.$t('attendance.clockIn') : this.$t('attendance.clockOut');
    },
  },
  methods: {
    handleReasonInput(event) {
      // 过滤表情符号
      const value = event.target.value;
      const filteredValue = value.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '');

      if (filteredValue !== value) {
        this.reason = filteredValue;
        event.target.value = filteredValue;
      }
    },
    handleConfirm() {
      // 验证原因是否为空
      if (!this.reason.trim()) {
        this.$toast(this.$t('attendance.reasonRequired'));
        return;
      }

      // 提交补卡申请，格式匹配API要求
      const fixClockInData = {
        signType: this.signType,
        reason: this.reason.trim(),
        attendanceDate: this.attendanceDate
      }

      // 触发提交事件
      this.$emit('submit', fixClockInData);

      // 重置表单
      this.resetForm();

      // 关闭弹窗
      this.$emit('input', false);
    },
    handleCancel() {
      this.resetForm();
      this.$emit('input', false);
    },
    resetForm() {
      this.reason = '';
    }
  },
  watch: {
    value(newVal) {
      if (newVal) {
        this.resetForm();
      }
    }
  }
};
</script>

<style scoped lang="scss">
.fix-clock-in-dialog-container {
  width: 600px;
  background-color: #ffffff;
  :deep(.van-dialog) {
    border-radius: 16px;
  }
}

.handling-exceptions-card {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  border-radius: 20px; /* 12px * 1.666... */
  box-shadow: 0 7px 20px rgba(0, 0, 0, 0.1); /* 4px*1.666 -> 7px, 12px*1.666 -> 20px */
  width: 600px; /* Target width */
  padding: 80px 44px 58px;
  color: #333;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(180deg, rgb(199, 224, 255) 0%, rgba(232, 242, 255, 0.4) 22.4%, rgba(255, 255, 255, 0) 44.2%);
}

.card-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 70px; /* 25px * 1.666... */
}

.title {
  font-size: 40px; /* 24px * 1.666... */
  font-weight: 600;
  color: #1e2a3b;
  margin: 0;
}

.close-button {
  position: absolute;
  top: 30px;
  right: 24px;
  width: 38px;
  height: 38px;
  cursor: pointer;
}

.close-button:hover {
  background-color: #f0f0f0;
}

.card-body {
  flex-grow: 1;
}

.form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px; /* 15px * 1.666... */
}

.form-label {
  font-size: 27px; /* 16px * 1.666... */
  font-weight: 600;
  color: #1e2a3b;
}

.action-link {
  font-size: 27px; /* 16px * 1.666... */
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.action-link:hover {
  text-decoration: underline;
}

.action-link .clock-type {
  color: #007bff;
}

.action-link .arrow {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-left: 8px; /* 5px * 1.666... */
  font-size: 33px; /* 20px * 1.666... */
  line-height: 1;
}

.separator {
  border: none;
  border-top: 1px solid #abb5ce; /* Kept at 1px */
  margin: 33px 0; /* 20px * 1.666... */
}

.reason-section {
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 33px; /* 20px * 1.666... */
}

.reason-section .form-label {
  margin-bottom: 17px; /* 10px * 1.666... */
}

.textarea-wrapper {
  position: relative;
  width: 100%;
}

textarea {
  width: 100%;
  min-height: 167px; /* 100px * 1.666... */
  padding-top: 17px; /* 10px * 1.666... */
  padding-bottom: 17px; /* 10px * 1.666... */
  padding-left: 20px; /* 12px * 1.666... */
  padding-right: 100px; /* 60px * 1.666... (for char counter) */
  border: 1px solid #d0d5dd; /* Kept at 1px */
  border-radius: 13px; /* 8px * 1.666... */
  background-color: #F1F3F6;
  font-family: inherit;
  font-size: 25px; /* 15px * 1.666... */
  color: #333;
  box-sizing: border-box;
  resize: vertical;
}

textarea::placeholder {
  color: #98a2b3;
}

.char-counter {
  position: absolute;
  bottom: 17px; /* 10px * 1.666... */
  right: 20px; /* 12px * 1.666... */
  font-size: 20px; /* 12px * 1.666... */
  color: #98a2b3;
}

.card-footer {
  margin-top: 33px; /* 20px * 1.666... */
}

.confirm-button {
  height: 88px;
  border-radius: 44px;
}
</style>
