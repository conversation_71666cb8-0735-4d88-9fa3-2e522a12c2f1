<template>
  <div class="button-selector-container">
    <div class="label" v-if="label">
      <span v-if="isRequired" class="label_required">*</span>{{label}}
    </div>
    <div class="buttons">
      <button
        v-for="option in options"
        :key="option.value"
        :class="['button', { active: selected === option.value }]"
        @click="selectOption(option.value)"
      >
        {{ option.label }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: "ButtonSelector",
  props: {
    isRequired: {
      type: Boolean,
      default: false
    },
    label: String,
    value: {
      type: [String, Number],
      default: "",
    },
    options: {
      type: Array,
      required: true, // 选项列表，格式为 [{ label: '显示文本', value: '选项值' }]
    },
  },
  data() {
    return {
      selected: this.value, // 当前选中的值
    };
  },
  watch: {
    // 同步父组件的选中值
    value(newVal) {
      this.selected = newVal;
    },
  },
  methods: {
    // 选择某个选项
    selectOption(value) {
      this.selected = value;
      this.$emit("input", value); // 支持 v-model 双向绑定
      this.$emit("change", value); // 自定义事件，通知父组件
    },
  },
};
</script>

<style scoped lang="scss">
.button-selector-container {

}

.label {
  padding-left: 24px;
  margin-bottom: 16px;
  color: #1B3155;
  font-size: 28px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  .label_required {
    color: #FF454F;
    margin-right: 6px;
    font-style: normal;
    font-weight: 400;
  }
}

.buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto; /* 居中显示 */
}

.button {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  height: 83px;
  border-radius: 16px;
  background-color: #f1f3f6;
  color: #1B3155;
  font-size: 30px;
  font-weight: 400;
  cursor: pointer;
  transition: background-color 0.15s, color 0.15s;
  border: none;
  &:first-of-type {
    margin-right: 24px;
  }
}

.button.active {
  background-color: #0074ff;
  color: white;
}
</style>
