<template>
  <div class="page-pop">
    <van-popup v-model="visible" :close-on-click-overlay="false">
      <div class="popup-content-show">
        <p class="title">Withdraw now for a higher credit limit on your next loan</p>
        <button class="c-button" @click="onChange">Get my cash</button>
        <h2 class="cancel" @click="onCancel(1)">Give up the chance</h2>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {

    };
  },

  mounted() {

  },
  methods: {
    onChange() {
      this.$emit('onChange')
    },
    onCancel() {
      this.$emit('onCancel')
      this.visible = false
      //finishAndroidWebPage()
    }
  }
};
</script>
<style lang="scss">
  .page-pop {
    /deep/ .van-popup--center {
      top: 0;
      left: 0;
      transform: initial;
      width: 100%;
      height: 100%;
      background: none;
    }
    /deep/ .van-overlay {
      background: rgba(0,0,0,0.50);
    }
    .cancel {
      color: #FFA600;
      font-size: 28px;
      text-align: center;
      margin-top: 30px;
    }
  }
</style>
