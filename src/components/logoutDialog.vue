<template>
  <van-dialog className="logout-dialog-container" :value="value" :show-cancel-button="false" :show-confirm-button="false">
    <div class="flex-col section_2">
      <div class="flex-col justify-start items-center text-wrapper"><span class="font text">{{ $t('logout.confirmText') }}</span></div>
      <div class="flex-col justify-start items-center relative text-wrapper_2" @click="exitWebview"><span class="text_2">{{ $t('logout.confirm') }}</span></div>
      <div class="flex-col justify-start items-center text-wrapper_3" @click="handleCancel"><span class="font text_3">{{ $t('logout.cancel') }}</span></div>
    </div>
  </van-dialog>
</template>

<script>
import { Dialog } from 'vant';
import { finishHtmlPage } from "@/api/native";
import { logout } from "@/axios/api";

export default {
  name: 'LogoutDialog',
  components: {
    [Dialog.Component.name]: Dialog.Component,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    exitWebview() {
      logout();
      this.$store.dispatch('removeAllToken');
      finishHtmlPage();
    },
    handleCancel() {
      this.$store.commit('SET_LOGOUT_DIALOG_VISIBLE', false);
    }
  },
};
</script>

<style scoped lang="scss">
.logout-dialog-container {
  width: 560px;
  :deep(.c-button) {
    height: 88px;
  }
}
.mt-25 {
  margin-top: 25px;
}
.section_2 {
  padding-bottom: 24px;
  background-color: #ffffff;
  border-radius: 16px;
  height: 466px;
  .text-wrapper {
    padding: 114.5px 0 181.5px;
    background-image: linear-gradient(180deg, #c7e0ff 0%, #e8f2ff66 62.4%, #ffffff00 84.2%);
    border-radius: 16px 16px 0px 0px;
    width: 560px;
    .text {
      color: #1b3155;
      line-height: 31px;
    }
  }
  .text-wrapper_2 {
    margin: -77px 24px 0;
    padding: 28.5px 0 31.5px;
    background-color: #0074ff;
    border-radius: 49px;
    width: 512px;
    .text_2 {
      color: #ffffff;
      font-size: 36px;
      line-height: 27.5px;
    }
  }
  .text-wrapper_3 {
    margin: 16px 24px 0;
    padding: 32px 0 31.5px;
    background-color: #d7d7d700;
    width: 512px;
    .text_3 {
      color: #7b8da8;
      line-height: 24.5px;
    }
  }
  .font {
    font-size: 32px;
  }
}
</style>
