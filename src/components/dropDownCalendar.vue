<template>
  <div class="select_box" :class="{'cancleTipClass': value}">
    <div class="select_label" v-if="label" :class="[value ? 'hasValue' : '']">
      <span v-if="isRequired" class="label_required">*</span>{{label}}
    </div>
    <div  @click="openSelect" class="select_inpt">
      <input
        type="text"
        :class="label ? '' : 'center'"
        :placeholder="placeholder"
        readonly
        :value="value"
      />
      <!--下拉箭头-->
      <img src="@/assets/images/common/ic_up.png" v-if="arrowStatus" class="arrowImg" />
      <img src="@/assets/images/common/ic_up.png" v-else class="arrowImg" />
    </div>
    <!--下拉弹出框  start-->
    <van-popup v-model="poupShow" position="bottom" style="height: 300px;" @close="closeMask">
      <DatetimePicker
        cancel-button-text=" "
        class="picker"
        v-model="curDate"
        type="date"
        :min-date="minDate"
        :max-date="maxDate"
        :columns-order="['day', 'month', 'year']"
        @confirm="confirm"
      />
    </van-popup>


  </div>
</template>

<script>
import { DatetimePicker } from 'vant';
import { monthToAbbreviation } from '@/utils/tools.js';
export default {
  name: "DropDownCalendar",
  components: {
    DatetimePicker
  },
  data() {
    return {
      //选中项的下标
      activeIndex: 0,
      //控制弹框的显示
      poupShow: false,
      //控制箭头的替换
      arrowStatus: false,
      //选中时显示“打勾”
      activeFlag: -1,
      //防止重复点击 (默认开始是可以点击的)
      clickStatus: true,
      // 勾选图标
      icolSel: require(`@/assets/images/common/select.png`),
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(),
      curDate: new Date()
    };
  },
  props: {
    value: {
      type: String,
      default: ""
    },
    labelProperty: {
      type: String,
      default() {
        return "name";
      }
    },
    label: String,
    placeholder: String,
    isRequired: {
      type: Boolean,
      default: false
    },
    currentDate: {
      type: Date,
      default() {
        return new Date();
      }
    }
  },
  methods: {
    //打开下拉弹出框
    openSelect() {
      console.log('openSelect')
      this.poupShow = true;
      this.arrowStatus = true;
    },
    //点击遮罩层时，替换下拉箭头
    closeMask() {
      this.arrowStatus = false;
      this.poupShow = false;
    },
    //点击取消，关闭
    closePopup() {
      this.arrowStatus = false;
      this.poupShow = false;
    },
    formatter(type, val) {
      if (type === 'month') {
        const newVal = monthToAbbreviation(val - 1);
        return `${newVal} ${this.$t('calendar.month')}`;
      } else if (type === 'day') {
        return `${val} ${this.$t('calendar.day')}`;
      } else if (type === 'year') {
        return `${val} ${this.$t('calendar.year')}`;
      }
      return val;
    },
    confirm(value) {
      console.log('value', value);
      this.$emit("change", {
        value: value
      });
      this.closePopup();
    }
  },
  mounted() {
    this.curDate = this.currentDate;
  },
  watch: {
    currentDate(value) {
      this.curDate = value;
    }
  }
};
</script>

<style scoped lang="scss">
.select_box {
  display: inline-block;
  width: 100%;
  position: relative;
  .van-overlay {
    opacity: 0.5;
    background: #000;
  }
  .van-popup--bottom {
    overflow-y: visible;
    background-color: #fff;
  }
  .select_label {
    position: absolute;
    top: calc(50% - 25px);
    left: 20px;
    color: #919DB3;
    z-index: 10;
    line-height: 1.5;
    transition: all .15s ease-in-out;
    pointer-events: none;
    font-size: 32px;
    &.hasValue {
      transform: scale(0.99);
      transform-origin: left;
      left: 20px;
      top: 0px;
      color: #536887;
      font-style: normal;
      @include themify() {
        color: themed("color");
      }
      font-size: 24px;
      font-weight: 700;
      line-height: normal;
      .label_required {
        position: relative;
        top: 6px;
      }
    }
    .label_required {
      color: #FF454F;
      font-size: 28px;
      font-style: normal;
      font-weight: 400;
      margin-right: 6px;
    }
  }
  .select_inpt {
    display: block;
    position: relative;
    color: #1B3155;
    font-size: 32px;
    font-style: normal;
    font-weight: 500;
    text-align: left;

    input {
      width: 100%;
      height: 110px;
      border: none;
      box-sizing: border-box;
      line-height: 19px;
      background: #fff;
      border-bottom: 1px solid #dbe1ea;
      padding-left: 24px;
      padding-top: 20px;
      color: #1b3155;
      font-size: 32px;
      font-weight: 500;
      &.center {
        padding-top: 0;
        line-height: 44px;
        font-size: 32px;
      }
    }

    input:disabled {
      background:#fff;
    }
    input::placeholder {
      color: #919DB3 !important;
      font-size: 32px !important;
      font-style: normal;
      font-weight: 500;
    }
    .arrowImg {
      width: 40px;
      height: 40px;
      position: absolute;
      right: 25px;
      top: 30px;
      transform: rotate(-90deg);
    }
  }
}
.picker {
  ::v-deep .van-picker__confirm{
    @include themify() {
      color: themed("color");
    }
  }
  ::v-deep .van-picker-column__wrapper{
    .van-picker-column__item--selected{
      @include themify() {
        color: themed("color");
      }
    }
  }
}
</style>
