<template>
  <van-dialog
    className="attendance-rules-dialog-container"
    :value="value"
    :show-cancel-button="false"
    :show-confirm-button="false"
    @click-overlay="handleClose"
  >
    <div class="flex-col section_2">
      <img src="@/assets/images/common/close1.png" class="close-button" alt="" @click="handleClose"/>
      <!-- 标题区域 -->
      <div class="flex-col justify-start items-center title-wrapper">
        <span class="font title-text">{{ $t('attendance.attendanceRulesTitle') }}</span>
      </div>

      <!-- 考勤规则内容 -->
      <div class="flex-col rules-content">
        <div class="rules-text">
          {{ getRulesText() }}
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<script>
import { Dialog } from 'vant';

export default {
  name: 'AttendanceRulesDialog',
  components: {
    [Dialog.Component.name]: Dialog.Component,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    attendanceRules: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false);
    },
    // 获取考勤规则文本
    getRulesText() {
      return this.$t('attendance.attendanceRulesContent');
    }
  }
};
</script>

<style scoped lang="scss">
.attendance-rules-dialog-container {
  width: 560px;
  :deep(.van-dialog) {
    border-radius: 16px;
  }
}

.section_2 {
  padding: 0 24px 24px;
  background-color: #ffffff;
  border-radius: 16px;
  min-height: 400px;
  background-image: linear-gradient(180deg, rgb(199, 224, 255) 0%, rgba(232, 242, 255, 0.4) 50%, rgba(255, 255, 255, 0) 10%);

  .title-wrapper {
    padding: 40px 0 30px;
    .title-text {
      color: #1b3155;
      font-size: 36px;
      line-height: 31px;
      font-weight: 600;
    }
  }

  .rules-content {
    flex: 1;
    margin-bottom: 30px;
    padding: 20px 0;

    .rules-text {
      color: #1b3155;
      font-size: 26px;
      line-height: 36px;
      text-align: left;
      white-space: pre-line;
    }
  }

  .close-button {
    position: absolute;
    top: 30px;
    right: 24px;
    width: 38px;
    height: 38px;
    cursor: pointer;
  }
}
</style>
