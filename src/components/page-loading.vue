<template>
  <div class="page-loading" v-if="show">
    <div class="custom-overlay">
      <div class="box">
        <van-loading :color="color" size="40px"></van-loading>
      </div>
    </div>
  </div>
</template>

<script>
import { Loading } from 'vant'
export default {
  components: {
    [Loading.name]: Loading
  },
  props: {
    show: Boolean
  },

  data() {
    return {
      color: '#FFA600'
    };
  },
  created(){
    const productSource = localStorage.getItem('productSource')
    switch (productSource) {
      case 'twigaloan':
        this.color = '#40B67A'
        break;  
      default:
        break;
    }
  },

  mounted() {

  }
};
</script>
<style lang="scss">
  .page-loading {
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    .custom-overlay {
      background-color: rgba(0, 0, 0, 0.65);
      width: 100%;
      height: 100%;
      padding: 0 16px;
      color: #fff;
      z-index: 2000;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .box {
      background: #fff;
      width: 160px;
      height: 160px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 16px;
    }
  }
</style>
