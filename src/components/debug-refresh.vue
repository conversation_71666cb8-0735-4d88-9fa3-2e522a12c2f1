<template>
  <div
    v-if="showRefreshButton"
    class="debug-refresh-btn"
    @click="refreshPage"
  >
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 4V9H4.58152M4.58152 9C5.24618 7.35817 6.43236 5.9735 7.96327 5.08223C9.49417 4.19095 11.2729 3.83555 13.033 4.06604C14.7931 4.29653 16.4287 5.10464 17.6551 6.35974C18.8815 7.61484 19.6379 9.2388 19.8079 10.9998M4.58152 9H9M20 20V15H19.4185M19.4185 15C18.7538 16.6418 17.5676 18.0265 16.0367 18.9178C14.5058 19.809 12.7271 20.1645 10.967 19.934C9.20688 19.7035 7.57133 18.8954 6.34493 17.6403C5.11853 16.3852 4.36209 14.7612 4.19207 13.0002M19.4185 15H15"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </div>
</template>

<script>
export default {
  name: 'DebugRefresh',
  data() {
    return {
      showRefreshButton: false
    };
  },
  created() {
    this.checkDebugMode();
  },
  methods: {
    /**
     * 检查是否显示调试刷新按钮
     * 根据localStorage中的refresh-debug参数决定
     */
    checkDebugMode() {
      const refreshDebug = localStorage.getItem('refresh-debug');
      // 只有当refresh-debug存在且不为空字符串时才显示
      this.showRefreshButton = refreshDebug !== null && refreshDebug !== '';

      if (this.showRefreshButton) {
        console.log('[DebugRefresh] 调试刷新按钮已启用');
      }
    },

    /**
     * 刷新页面方法
     */
    refreshPage() {
      console.log('[DebugRefresh] 执行页面刷新');
      window.location.reload();
    }
  }
};
</script>

<style lang="scss" scoped>
/* 调试刷新按钮样式 */
.debug-refresh-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 44px;
  height: 44px;
  background-color: rgba(0, 116, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 9999;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(0, 116, 255, 1);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

/* 在小屏幕上调整按钮大小 */
@media (max-width: 480px) {
  .debug-refresh-btn {
    width: 40px;
    height: 40px;
    top: 15px;
    right: 15px;
  }
}
</style>
