<template>
  <div class="flex-col group">
    <span v-if="pageName" class="self-start text_3">{{pageName}}</span>
    <div class="flex-row self-stretch group_3">
      <span class="font_2 text_4">{{$t('personInfo.email')}}:</span>
      <span class="font_3 text_5 ml-4">{{ userInfo && userInfo.email }}</span>
    </div>
    <div class="flex-row self-stretch group_3">
      <span class="font_2 text_4">{{$t('personInfo.phoneNumber')}}:</span>
      <span class="font_3 text_5 ml-4">{{ userInfo && userInfo.phone }}</span>
    </div>
    <safety-note />
  </div>
</template>

<script>
import SafetyNote from './safety-note.vue';
import { mapState } from "vuex";
export default {
  components: {
    SafetyNote,
  },
  props: {
    pageName: {
      type: String,
      default: ''
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapState(['userInfo'])
  }
};
</script>

<style scoped lang="scss">
.ml-17 {
  margin-left: 17px;
}
.mt-15 {
  margin-top: 15px;
}
.group {
  padding: 14px 24px 23px;
  .group_2 {
    line-height: 14.5px;
    height: 14.5px;
    .font {
      font-size: 20px;
      line-height: 14.5px;
      color: #314250;
    }
    .text_2 {
      color: #314250;
      font-size: 20px;
      line-height: 10px;
    }
    .text {
      line-height: 14px;
    }
  }
  .image {
    width: 28px;
    height: 21px;
  }
  .image_2 {
    width: 17px;
    height: 20px;
  }
  .image_3 {
    width: 34px;
    height: 18px;
  }
  .image_4 {
    margin-left: 6px;
    margin-top: 35px;
    width: 44px;
    height: 44px;
  }
  .text_3 {
    color: #1b3155;
    font-size: 40px;
    font-weight: 700;
    line-height: 31px;
  }
  .group_3 {
    margin-top: 24.5px;
    .font_2 {
      font-size: 24px;
      color: #7b8da8;
    }
    .text_4 {
      line-height: 18px;
    }
    .font_3 {
      font-size: 24px;
      line-height: 18px;
      font-weight: 700;
      color: #0074ff;
    }
    .text_5 {
      color: #1b3155;
      line-height: 17.5px;
    }
  }
  .section {
    margin-top: 28px;
    padding: 19.5px 13px 0 24px;
    background-color: #d1e7ff;
    border-radius: 16px 16px 0px 16px;
    .image_5 {
      margin-top: 9.5px;
      width: 37px;
      height: 38px;
    }
    .font_4 {
      font-size: 28px;
      font-weight: 700;
    }
    .text_6 {
      color: #0074ff;
      line-height: 21.5px;
    }
    .image_6 {
      width: 159px;
      height: 39px;
    }
    .font_5 {
      font-size: 24px;
      line-height: 23px;
      font-weight: 700;
      color: #0074ff;
    }
    .text_7 {
      color: #4773b6;
      line-height: 23.5px;
      font-weight: unset;
    }
    .pos {
      position: absolute;
      left: 0;
      top: 0;
    }
  }
}
</style>
