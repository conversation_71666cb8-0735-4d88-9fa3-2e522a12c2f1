import { Toast } from "vant";
import { isIOS } from "@/utils/tools";
import { DEVICE_TYPE } from '@/enums/device'

export const showToast = (str, pos) => {
  Toast({
    message: str,
    position: pos || 'middle'
  })
}

export const getCallbackName = (methodName) => `_${methodName}_callback_${Date.now()}_${Math.floor(Math.random() * 1000)}`;


/**
 * 与原生环境进行异步通信，并返回一个 Promise。
 * 解决了内存泄漏、缺少超时和回调冲突的问题。
 *
 * @param {string} methodName - 要调用的原生方法的名称。
 * @param {object} data - 要传递给原生方法的数据。
 * @param {object} options - 可选参数对象，包含以下属性：
 *   - {number} timeout - 调用原生方法的超时时间，单位为毫秒，默认为 10000 毫秒。
 *   - {boolean} needTimeout - 是否需要超时处理，默认为 true。
 * @returns {Promise<object>} - 返回一个解析为原生返回数据的 Promise。
 */
const executeWithCallback = (methodName, data, options = {}) => {
  const { timeout = 10000, needTimeout = true } = options;

  return new Promise((resolve, reject) => {
    const callbackName = getCallbackName(methodName);
    let timeoutId = null;

    const cleanup = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      if (window[callbackName]) {
        delete window[callbackName];
      }
    };

    if (needTimeout) {
      timeoutId = setTimeout(() => {
        cleanup();
        reject(new Error(`Native call timed out for method: ${methodName} after ${timeout}ms`));
      }, timeout);
    }

    window[callbackName] = (res) => {
      if (needTimeout) {
        cleanup();
      }

      console.log(`🎉 ~ Native callback received for ${methodName}. Raw response: `, res);

      try {
        if (res === null || typeof res === 'undefined') {
          resolve({});
          return;
        }

        const result = typeof res === 'string' ? JSON.parse(res) : res;
        resolve(result || {});
      } catch (error) {
        console.error(`Failed to parse native response for ${methodName}:`, {
          error,
          rawResponse: res,
        });
        reject(new Error(`Failed to parse native response. Raw: ${res}`));
      }
    };

    try {
      const jsonData = JSON.stringify(data || {});
      if (window.flutter_inappwebview?.callHandler) {
        console.log(`🚀 ~ Calling Flutter: ${methodName} with params:`, data);
        window.flutter_inappwebview.callHandler('flutterHandler', methodName, jsonData, callbackName);
      } else if (window.quickSave?.[methodName]) {
        console.log(`🚀 ~ Calling quickSave: ${methodName} with params:`, data);
        window.quickSave[methodName](jsonData, callbackName);
      } else {
        if (needTimeout) {
          cleanup();
        }
        reject(new Error(`Native method not found: ${methodName}`));
      }
    } catch (e) {
      if (needTimeout) {
        cleanup();
      }
      reject(e);
    }
  });
};

export const getGpsInfo = async (data) => {
  try {
    console.log('getGpsInfo()');
    const result = await executeWithCallback('getGpsInfo', data);
    return result;
  } catch (error) {
    console.log('调用 getGpsInfo 失败');
    throw error;
  }
};

// getWifiInfo
export const getWifiInfo = async (data) => {
  try {
    console.log('getWifiInfo()');
    const result = await executeWithCallback('getWifiInfo', data);
    return result;
  } catch (error) {
    console.log('调用 getWifiInfo 失败');
    throw error;
  }
}

// startSystemCapture
export const startSystemCapture = async (data) => {
  try {
    console.log('startSystemCapture()');
    const result = await executeWithCallback('startSystemCapture', data);
    return result;
  } catch (error) {
    console.log('调用 startSystemCapture 失败');
    throw error;
  }
}

// startRecord
export const startRecord = async (data) => {
  try {
    console.log('startRecord()');
    const result = await executeWithCallback('startRecord', data);
    return result;
  } catch (error) {
    console.log('调用 startRecord 失败');
    throw error;
  }
}

// stopRecord
export const stopRecord = async (data) => {
  try {
    console.log('stopRecord()');
    const result = await executeWithCallback('stopRecord', data);
    return result;
  } catch (error) {
    console.log('调用 stopRecord 失败');
    throw error;
  }
}

// checkPermissions
export const checkPermissions = async (data) => {
  try {
    console.log('checkPermissions()');
    const result = await executeWithCallback('checkPermissions', data, { needTimeout: false });
    return result;
  } catch (error) {
    console.log('调用 checkPermissions 失败');
    throw error;
  }
}

// requestPermissions
export const requestPermissions = async (data) => {
  try {
    console.log('requestPermissions()');
    const result = await executeWithCallback('requestPermissions', data, { needTimeout: false });
    return result;
  } catch (error) {
    console.log('调用 requestPermissions 失败');
    throw error;
  }
}
// startFileChooser
export const startFileChooser = async (data) => {
  try {
    console.log('startFileChooser()');
    const result = await executeWithCallback('startFileChooser', data);
    return result;
  } catch (error) {
    console.log('调用 startFileChooser 失败');
    throw error;
  }
}

// getAppConfig
export const getAppConfig = async (data) => {
  try {
    console.log('getAppConfig()');
    const result = await executeWithCallback('getAppConfig', data);
    // 动态设置设备类型，优先使用原生返回的设备类型
    const deviceType = result.deviceType || (isIOS() ? DEVICE_TYPE.APPLE_PHONE : DEVICE_TYPE.ANDROID_PHONE);
    return {
      ...result,
      deviceType
    };
  } catch (error) {
    console.log('调用 getAppConfig 失败');
    throw error;
  }
}

export const loginEM = async (data) => {
  try {
    console.log('loginEM()');
    const result = await executeWithCallback('loginEM', data, { needTimeout: false });
    return result;
  } catch (error) {
    console.log('调用 loginEM 失败');
    throw error;
  }
}

export const uploadData = async (data) => {
  try {
    console.log('uploadData()');
    const result = await executeWithCallback('uploadData', data, { needTimeout: false });
    return result;
  } catch (error) {
    console.log('调用 uploadData 失败');
    throw error;
  }
}

export const finishHtmlPage = async (data) => {
  try {
    console.log('finishHtmlPage()');
    const result = await executeWithCallback('finishHtmlPage', data, { needTimeout: false });
    return result;
  } catch (error) {
    console.log('调用 finishHtmlPage 失败');
    throw error;
  }
}

// getUid
export const getUid = async (data) => {
  try {
    console.log('getUid()');
    const result = await executeWithCallback('getUid', data);
    return result;
  } catch (error) {
    console.log('调用 getUid 失败');
    throw error;
  }
}

export const enableNativeBack = () => {
  window.nativeBackCallback = function() {
    console.log('nativeBackCallback: return 0');
    // 1 原生不处理返回键  0 原生处理返回键
    return 0;
  }
}

export const disableNativeBack = (callback) => {
  window.nativeBackCallback = function() {
    console.log('nativeBackCallback: return 1');
    // 1 原生不处理返回键  0 原生处理返回键
    callback?.();
    return 1;
  }
}

export const sendCode = async (data) => {
  try {
    console.log('sendCode()');
    const result = await executeWithCallback('sendCode', data, { needTimeout: false });
    return result;
  } catch (error) {
    console.log('调用 sendCode 失败');
    throw error;
  }
}

