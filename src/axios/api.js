import axios from './request';

export const checkEmail = function (data) {
  return axios({
    method: 'post',
    url: '/auth/checkEmail',
    data,
    useToken: false,
  });
};

export const sendEmailCode = function (data) {
  return axios({
    method: 'post',
    url: '/auth/emailCode',
    data,
    useToken: false,
  });
};

export const checkEmailCode = function (data) {
  return axios({
    method: 'post',
    url: '/auth/checkEmailCode',
    data,
    useToken: false,
  });
};

export const authByCode = function (data) {
  return axios({
    method: 'post',
    url: '/auth/key',
    data,
    useToken: false,
  });
};

export const logout = function (data) {
  return axios({
    method: 'post',
    url: '/auth/logout',
    data,
  });
};

export const appTo = function (data) {
  return axios({
    method: 'post',
    url: '/app/to',
    data,
    loading: false,
    showMessage: false
  });
};

export const fetchInviteInfo = function (data) {
  return axios({
    method: 'post',
    url: '/invite/info',
    data,
  });
};

export const fetchUserIdType = function (data) {
  return axios({
    method: 'post',
    url: '/invite/user/idType',
    data,
  });
};

export const inviteSubmit = function (data) {
  return axios({
    method: 'post',
    url: '/invite/submit',
    data,
  });
};

export const inviteUpdate = function (data) {
  return axios({
    method: 'post',
    url: '/invite/update',
    data,
  });
}

export const uploadFile = function (data) {
  console.log('🎉 ~ file: api.js ~ line: 91 ~ : uploadFile');
  return axios({
    method: 'post',
    url: '/file/upload',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const getSignCurrentSign = function (data) {
  return axios({
    method: 'post',
    url: '/attendance/clock/current',
    data,
  });
};

export const signIn = function (data) {
  return axios({
    method: 'post',
    url: '/attendance/clock/in',
    data,
  });
};

// 获取考勤规则接口
export const getAttendanceRule = function (data) {
  return axios({
    method: 'post',
    url: '/attendance/rule',
    data,
  });
};

// 获取考勤日历记录接口
export const getAttendanceClockRecord = function (data) {
  return axios({
    method: 'post',
    url: '/attendance/clock/record',
    data,
  });
};

// 提交补卡申请接口
export const submitAttendanceApproval = function (data) {
  return axios({
    method: 'post',
    url: '/attendance/approval/apply',
    data,
  });
};

// 上报日志接口
export const reportLog = function (data) {
  return axios({
    method: 'post',
    url: '/log/api',
    data,
    loading: false, // 不显示loading，避免影响用户体验
    showMessage: false // 不显示错误消息，避免干扰正常打卡流程
  });
};
