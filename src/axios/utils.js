export class RandomHelper {
  constructor() {
    this.availableChars = "abcdefghijklmnopqrstuvwxyz1234567890";
  }

  randomLowerCaseString(length) {
    var randomString = Array.from(
      { length: length },
      (_, i) =>
        this.availableChars[
          Math.floor(Math.random() * this.availableChars.length)
          ]
    ).join("");
    return randomString;
  }

  randomUUID() {
    var a = this.randomLowerCaseString(8);
    var b = this.randomLowerCaseString(4);
    var c = this.randomLowerCaseString(4);
    var d = this.randomLowerCaseString(4);
    var e = this.randomLowerCaseString(12);
    return a + "-" + b + "-" + c + "-" + d + "-" + e;
  }
}

export function returnBaseUrl(proxyUrl = "", url) {
  // console.log(process.env, "process.env3333");
  let returnBaseUrl = "";
  if (process.env.NODE_ENV === "production") {
    returnBaseUrl = `${process.env.VUE_APP_REQUESTURL || ""}${proxyUrl}`;
  } else {
    // 开发环境走代理
    returnBaseUrl = proxyUrl;
  }
  return returnBaseUrl;
}
