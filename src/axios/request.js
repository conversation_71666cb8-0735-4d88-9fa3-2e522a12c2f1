import axios from "axios";
import { Toast } from "vant";
import { errorHandler, errorMsgHandler } from "./errorHandler";
import { RandomHelper } from "./utils";
import router from "@/router";
import { i18n, transformLang } from "@/i18n";
import { finishHtmlPage, sendCode } from "@/api/native";

const pendingMap = new Map();
let requestCount = 0;
let loadingInstance;
const options = {
  forbidClick: true,
  className: "request-loading",
  loadingType: "spinner",
};
const showLoading = () => {
  requestCount++;
  if (requestCount === 1) {
    loadingInstance = Toast.loading(options);
  }
};
const closeLoading = () => {
  requestCount--;
  if (requestCount === 0) loadingInstance.clear();
};

function getRequestKey(config) {
  return (
    (config.method || "") + config.url + "?" + JSON.stringify(config?.data)
  );
}
const service = axios.create({
  timeout: 1000 * 60 * 5,
  baseURL: "/dxe-service",
  withCredentials: true,
  loading: true,
  showMessage: true,
  headers: {
    "Content-Type": "application/json;charset=utf-8",
  },
});

service.interceptors.request.use(
  (config) => {
    const now = +new Date();
    // console.log(config, "request");

    var randomHelper = new RandomHelper();
    var randomUUID = randomHelper.randomUUID();
    const tempToken = localStorage.getItem("tempToken") || "";
    const token = localStorage.getItem("token") || "";
    const lang = localStorage.getItem("lang") || "en";

    config.headers["X-Request-Id"] = randomUUID;
    config.headers.CurrentTime = now;
    const useToken = config.useToken === undefined ? true : config.useToken;
    if (useToken) {
      if (tempToken) config.headers["T-Token"] = tempToken;
      if (token) config.headers.Token = token;
    }
    if (lang) config.headers["Accept-Language"] = transformLang(lang);
    if (config.loading) showLoading();
    // 如果content-type是formData
    if (config.headers["Content-Type"] !== "multipart/form-data") {
      const data = config.data ? config.data : {};
      config.data = {
        data
      }
    }
    return config;
  },
  (error) => {
    console.log(error);
    return Promise.reject(error);
  }
);

service.interceptors.response.use((response) => {
  const config = response.config;
  if (config.loading) closeLoading();
  const key = getRequestKey(config);
  if (pendingMap.has(key)) {
    pendingMap.delete(key);
  }
  console.log('🎉 ~ file: request.js ~ line: 89 ~ response: ', response);
  if (response.status === 200) {
    const { data, status } = response.data;
    if (status.code === 0) {
      return Promise.resolve(data);
    } else {
      console.trace('2212');
      const profileChangedCodes = [33305005, 33305001, 33303001, 33305006];
      // 5: token失效
      if (status.code === 5 || profileChangedCodes.includes(status.code)) {
        localStorage.removeItem('token');
        localStorage.removeItem('tempToken');
        router.replace('/login-input');
        if (status.code === 5) {
          const from = localStorage.getItem('from') || '';
          const country = localStorage.getItem('country') || '';
          sendCode({ code: 'null', from, country });
          finishHtmlPage();
        }
      }
      let msg = status.msg;
      if (profileChangedCodes.includes(status.code)) {
        msg = i18n.t('toastMsg.profileChanged');
      }
      if (config.showMessage) {
        errorHandler(msg, config.errorMode);
      }
      return Promise.reject(status);
    }
  } else {
    console.log(11111);
    const errMsg = errorMsgHandler(response.status);
    if (config.showMessage) {
      errorHandler(errMsg, config.errorMode);
    }
    return Promise.reject(errMsg);
  }
}, (e) => {
  if (loadingInstance) {
    loadingInstance.clear();
  }
  if (e && e?.code !== "ERR_CANCELED") {
    let message = e.message || e.msg;
    if (message === 'Network Error') {
      message = i18n.t('toastMsg.networkError');
    }
    if (e.config?.showMessage) {
      Toast({
        message
      });
    }
  }
  return Promise.reject(e);
});

export default service;
