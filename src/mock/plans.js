// 试算接口 activeRepaymentCalc
const plans = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    amtPaid: 0,
    amtOutstd: 163786,
    actualAmtOutstd: 163786,
    remainingRepaymentAmount: 0,
    principal: 75000,
    interest: 85555.56,
    defaultInterest: 1653.75,
    vatAmount: 1576.07,
    txnScene: null,
    coolingOffINTFreeFlag: null,
    serviceCharge: 1653.75,
    penaltyCharge: 0,
    promiseValidDate: null,
    loanList: [
      {
        loanId: "528596306626617344",
        term: 2,
        amtPaid: 0,
        amt: 163786,
        actualAmt: 163786,
        txnStatus: null,
        coolingOffINTFreeFlag: false,
        txnScene: null,
        loanDay: 0,
        requestSessionId: null,
        plans: [
          {
            overdueInterest: "0.00",
            fees: [{ VAT_FEE_PAID: "0.00", VAT_FEE_OUTSTD: "147.00" }],
            overdueInterestOutstd: "0.00",
            dueDate: "2023-03-25",
            termPromiseAmt: 0,
            termRemainAmtOutstd: 43990,
            principalPaid: "0.00",
            termPromiseValidDate: "",
            termOriginAmtOutstd: 43990,
            amountActualOutstd: 43990,
            amountPaid: "0.00",
            amountOutstd: 43990,
            termStatus: "N",
            term: 1,
            principalOutstd: "33108.54",
            interestOutstd: "10290.00",
            interestPaid: "0.00"
          },
          {
            overdueInterest: "0.00",
            fees: [],
            overdueInterestOutstd: "0.00",
            dueDate: "2023-04-01",
            termPromiseAmt: 0,
            termRemainAmtOutstd: 43842,
            principalPaid: "0.00",
            termPromiseValidDate: "",
            termOriginAmtOutstd: 43842,
            amountActualOutstd: 43842,
            amountPaid: "0.00",
            amountOutstd: 43842,
            termStatus: "N",
            term: 2,
            principalOutstd: "35426.13",
            interestOutstd: "7972.41",
            interestPaid: "0.00"
          },
          {
            overdueInterest: "0.00",
            fees: [],
            overdueInterestOutstd: "0.00",
            dueDate: "2023-04-08",
            termPromiseAmt: 0,
            termRemainAmtOutstd: 43842,
            principalPaid: "0.00",
            termPromiseValidDate: "",
            termOriginAmtOutstd: 43842,
            amountActualOutstd: 43842,
            amountPaid: "0.00",
            amountOutstd: 43842,
            termStatus: "N",
            term: 3,
            principalOutstd: "37905.96",
            interestOutstd: "5492.58",
            interestPaid: "0.00"
          },
          {
            overdueInterest: "0.00",
            fees: [],
            overdueInterestOutstd: "0.00",
            dueDate: "2023-04-15",
            termPromiseAmt: 0,
            termRemainAmtOutstd: 43842,
            principalPaid: "0.00",
            termPromiseValidDate: "",
            termOriginAmtOutstd: 43842,
            amountActualOutstd: 43842,
            amountPaid: "0.00",
            amountOutstd: 43842,
            termStatus: "N",
            term: 4,
            principalOutstd: "40559.37",
            interestOutstd: "2839.17",
            interestPaid: "0.00"
          }
        ],
        feeOutstd: 1576.07
      }
    ],
    repayAmtTipMsg:
      "The min repayment amount is TZS 200, pls reserve sufficient fee for Mpesa channel fee, pls repay on time, overdue will affert personal credit"
  }
};

const plansKenya = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    amtPaid: 0,
    amtOutstd: 170605,
    actualAmtOutstd: 170605,
    remainingRepaymentAmount: 0,
    principal: 75000,
    interest: 85555.56,
    defaultInterest: 8337.6,
    vatAmount: 1576.07,
    txnScene: null,
    coolingOffINTFreeFlag: null,
    serviceCharge: 8337.6,
    penaltyCharge: 135,
    promiseValidDate: null,
    loanList: [
      {
        loanId: "528596306626617344",
        term: 2,
        amtPaid: 0,
        amt: 170605,
        actualAmt: 170605,
        txnStatus: null,
        coolingOffINTFreeFlag: false,
        txnScene: null,
        loanDay: 0,
        requestSessionId: null,
        plans: [
          {
            overdueInterest: "0.00",
            fees: [{ VAT_FEE_PAID: "0.00", VAT_FEE_OUTSTD: "1576.07" }],
            overdueInterestOutstd: "4500.00",
            dueDate: "2023-03-13",
            termRemainAmtOutstd: 87235,
            principalPaid: "0.00",
            amountActualOutstd: 87235,
            amountPaid: "0.00",
            amountOutstd: 87235,
            termStatus: "O",
            term: 1,
            principalOutstd: "27777.78",
            interestOutstd: "52500.00",
            interestPaid: "0.00"
          },
          {
            overdueInterest: "0.00",
            fees: [],
            overdueInterestOutstd: "2250.00",
            dueDate: "2023-03-20",
            termRemainAmtOutstd: 83370,
            principalPaid: "0.00",
            amountActualOutstd: 83370,
            amountPaid: "0.00",
            amountOutstd: 83370,
            termStatus: "O",
            term: 2,
            principalOutstd: "47222.22",
            interestOutstd: "33055.56",
            interestPaid: "0.00"
          }
        ],
        feeOutstd: 1576.07
      }
    ],
    repayAmtTipMsg:
      "The min repayment amount is TZS 200, pls reserve sufficient fee for Mpesa channel fee, pls repay on time, overdue will affert personal credit"
  }
};

export default {
  "get|/parameter/plans": plansKenya
};
