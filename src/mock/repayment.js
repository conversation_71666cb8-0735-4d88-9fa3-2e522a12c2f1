const loanList = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    loanList: [
      {
        custId: "515587975112826880",
        acctId: "515588022542016512",
        acctType: "PDL-HC",
        loanId: "515925565775028224",
        loanType: "OOP",
        loanStatus: "N",
        loanAmt: 50000,
        loanTerm: 1,
        outstdBal: 54552,
        currTerm: 1,
        productId: "0008",
        productFeeId: "0081",
        applyNo: "A515925376372838400",
        busiDate: "2022-12-19",
        overdueDays: 0,
        earliestOverdueDate: null,
        overdueDate: null,
        cpdBeginDate: null,
        cpdDays: null,
        paidOffDate: null,
        nextDueDate: "2022-12-25"
      }
    ],
    totalAmt: 54551.07,
    totalCount: 1,
    currDate: "2022-12-19"
  }
};

// 试算接口数据
const repay = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    amtPaid: 0,
    amtOutstd: 55108,
    actualAmtOutstd: 55108,
    remainingRepaymentAmount: 0,
    principal: 50000,
    interest: 3500,
    defaultInterest: 556.43,
    vatAmount: 1051.07,
    txnScene: null,
    coolingOffINTFreeFlag: null,
    serviceCharge: 556.43,
    penaltyCharge: 0
  }
};

const accountType = {
  traceId: "595c8becc631dc8a",
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    mpCode: "*********",
    repayCode: "*********",
    accountType: "Mpesa"
  }
};

const methods = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: [
    {
      value: "PayBill Repay",
      label: "PayBill Repay",
      type: "PaymentMethods",
      sort: "1",
      remarks: "0",
      tag: "M-PESA Paybill"
    },
    {
      value: "PAY_BY_CARD",
      label: "PAY_BY_CARD",
      type: "PaymentMethods",
      sort: "2",
      remarks: null,
      tag: "M-PESA Express"
    }
  ]
};

const cardData = {
  traceId: "d040b5ab193b46b5",
  status: {
    code: 0,
    msg: "OK"
  },
  data: [
    {
      cardId: "6947",
      cardNum: "*********",
      status: "VALID",
      createTime: "Thu Nov 24 08:23:15 UTC 2022",
      fullName: "automation test",
      bankName: "Mpesa",
      bankIcon: "",
      cardType: "",
      channel: "mshikoapp",
      expireYear: "99",
      expereMonth: "12",
      defaultFlag: "Y"
    }
  ]
};

// 点击还款按钮
const repaymentclick = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    custId: "515928419898236928",
    loanId: "515929340531183616",
    orderId: "********************",
    orderStatus: "P",
    createTime: "2022-11-24 11:43:55",
    actualAmt: 55108,
    coolingOffINTFreeFlag: "N"
  }
};

const orderData = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    orderId: "********************",
    applyNo: "A515928659845980160",
    custId: "515928419898236928",
    txnDirection: "C",
    acctType: "PDL-HC",
    loanId: "515929340531183616",
    productFeeId: null,
    loanTerm: null,
    actualAmt: 55108,
    orderStatus: "P",
    paymentStatus: "P",
    resultCode: null,
    resultMessage: null
  }
};

export default {
  "get|/parameter/queryLoantList": loanList,
  "get|/parameter/queryRepay": repay,
  "get|/parameter/queryAccountType": accountType,
  "get|/parameter/queryMethods": methods,
  "get|/parameter/queryCardData": cardData,
  "get|/parameter/repaymentclick": repaymentclick,
  "get|/parameter/orderData": orderData
};
