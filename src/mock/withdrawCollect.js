const withdrawCollect = {
  mutuallyExclusiveFlag: false,
  custLmtVO: {
    lmtAmt: 50000,
    usedLmtAmt: 0,
    surpLmtAmt: null,
    usableLmtAmt: 50000,
    increaseAmt: null,
    replenishCardFlag: null
  },
  productListVO: {
    productList: [
      {
        productId: "1801",
        acctType: "7D",
        loanType: "OOP",
        txnScene: "C",
        productFee: [
          {
            pltffeeAmt: 0,
            productFeeId: "1801",
            insuranceRate: 0.01,
            vatRate: 0.03,
            maxAmt: 50000,
            minAmt: 50000,
            customerLevel: "A,A1,C,D,E,F,S,R,B",
            pltfRate: 0.09,
            rate: 0.1,
            vatfeeAmt: 15,
            dayRate: 0.1,
            term: 1,
            contractCode: "12132",
            handingFeeRate: 0.12,
            totalBorrowCount: 7
          }
        ],
        borrowCount: 7,
        lmtRate: 1
      },
      {
        productId: "1801",
        acctType: "14D",
        loanType: "OOP",
        txnScene: "C",
        productFee: [
          {
            pltffeeAmt: 0,
            productFeeId: "1802",
            insuranceRate: 0.01,
            vatRate: 0,
            maxAmt: 50000,
            minAmt: 50000,
            customerLevel: "A,A1,C,D,E,F,S,R,Y,B",
            pltfRate: 0,
            rate: 0.01,
            vatfeeAmt: 0,
            dayRate: 0.01,
            term: 1,
            contractCode: "12132",
            handingFeeRate: 0,
            totalBorrowCount: 14
          }
        ],
        borrowCount: 14,
        lmtRate: 1
      }
    ],
    currDate: "1687824000000",
    defaultFeeId: "1802",
    productShowType: "A"
  },
  loanCalculateVO: {
    principal: "50000.00",
    totalAmount: "57600.00",
    actualTotalAmount: "50000.00",
    interest: "7000.00",
    plans: [
      {
        termPrincipal: "50000.00",
        currentTerm: 1,
        termAmount: "57600.00",
        dueDate: "2023-07-10",
        termInterest: "7000.00",
        termFeeList: [{ INSURANCE_FEE: "600.00" }]
      }
    ],
    fee: "600.00",
    deadline: "2023-07-10",
    loanCalcVOList: [
      {
        isCheckBoxFlag: true,
        value: 600,
        key: "Accident Protection Service",
        tips: "In the event of death or disability, we will waive your loan balance"
      },
      { value: "50000.00", key: "Received" },
      { value: "2023-07-10", key: "Due Date" },
      { value: "57600.00", key: "Repayment Amount" }
    ]
  }
};

export default {
  "get|/parameter/withdrawCollect": withdrawCollect
};
