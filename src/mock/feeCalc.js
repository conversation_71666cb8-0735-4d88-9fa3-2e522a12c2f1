const reqParamSingle = {
  data: {
      loanAmt: "50000.0",
      loanSpan: "7",
      loanTerm: "1",
      loanType: "OOP",
      productFeeId: "1801",
      txnScene: "C"
  }
}

// 单期试算结果
const feeSingle = {
  status: {
      code: 0,
      msg: "OK"
  },
  data: {
      totalAmount: "86051.07",
      fee: "16052.07",
      actualTotalAmount: "34999.00",
      principal: "50000.00",
      interest: "35000.00",
      orderTime: null,
      deadline: "2023-02-08",
      bankCardName: null,
      contractHtml: null,
      phoneNumber: null,
      plans: [
          {
              termPrincipal: "50000.00",
              currentTerm: 1,
              termAmount: "86051.07",
              dueDate: "2023-02-08",
              termInterest: "35000.00",
              termFeeList: [
                  {
                      PLTF_FEE: "15001.00"
                  },
                  {
                      VAT_FEE: "1051.07"
                  }
              ]
          }
      ],
      loanCalcVOList: [
          {
              key: "Platform Fee",
              value: "15001.00"
          },
          {
              key: "Received",
              value: "34999.00"
          },
          {
              key: "Due Date",
              value: "2023-02-08"
          },
          {
              key: "Repayment Amount",
              value: "86051.07"
          }
      ]
  }
}


const reqParam = {
  data: {
      loanAmt: "75000.0",
      loanSpan: "7",
      loanTerm: "2",
      loanType: "EPI",
      productFeeId: "1702",
      txnScene: "C"
  }
}

// 多期试算结果
const fee = {
    status: {
        code: 0,
        msg: "OK"
    },
    data: {
        totalAmount: "173594.16",
        fee: "0.00",
        actualTotalAmount: "147000.00",
        principal: "147000.00",
        interest: "26594.16",
        orderTime: null,
        deadline: "2023-03-25",
        bankCardName: null,
        contractHtml: null,
        phoneNumber: null,
        plans: [
            {
                termPrincipal: "33108.54",
                currentTerm: 1,
                termAmount: "43398.54",
                dueDate: "2023-03-25",
                termInterest: "10290.00",
                termFeeList: [

                ]
            },
            {
                termPrincipal: "35426.13",
                currentTerm: 2,
                termAmount: "43398.54",
                dueDate: "2023-04-01",
                termInterest: "7972.41",
                termFeeList: [

                ]
            },
            {
                termPrincipal: "37905.96",
                currentTerm: 3,
                termAmount: "43398.54",
                dueDate: "2023-04-08",
                termInterest: "5492.58",
                termFeeList: [

                ]
            },
            {
                termPrincipal: "40559.37",
                currentTerm: 4,
                termAmount: "43398.54",
                dueDate: "2023-04-15",
                termInterest: "2839.17",
                termFeeList: [

                ]
            }
        ],
        loanCalcVOList: [
            {
                key: "Received",
                value: "147000.00"
            }
        ]
    }
}

export default {
  "get|/parameter/getFeeCalcSingle": feeSingle,
  "get|/parameter/getFeeCalc": fee
};