const accountData = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    cardId: "4735",
    cardNum: "*********",
    status: "VALID",
    createTime: "Wed Nov 23 05:43:08 UTC 2022",
    fullName: "automation test",
    bankName: "Tigo",
    bankIcon:
      "https://testing-palmcredit-public.s3.cn-northwest-1.amazonaws.com.cn/tz-images/ic_tigo.png",
    cardType: "",
    channel: "mshikoapp",
    expireYear: "99",
    expereMonth: "12",
    defaultFlag: "Y",
    accountType: "Tigopesa",
    accountTypeAlias: "Tigopesa Online"
  }
};

const account = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    operator: "Tigo",
    accountType: "Tigopesa",
    logoIcon:
      "https://testing-palmcredit-public.s3.cn-northwest-1.amazonaws.com.cn/tz-images/ic_tigo.png",
    errorMsg: null
  }
};

// 提现页面，查询银行账户
/**
 * crd/bank/queryWithdrawAcctInfo
 * {
    "data":{
        "multipleAccounts":"true", // 是否展示多个账户
        "custId":"***********"
    }
}
 */
const accountInfo = {
  traceId: "21440238ee31f285",
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    bankAcctList: [
      {
        bankCode: "057",
        bankName: "ZENITH BANK PLC",
        bankAcctNo: "**********",
        channel: null,
        bankAcctName: null
      }
    ],
    isBvnIdentical: false
  }
};

export default {
  "get|/parameter/queryAccountData": accountData,
  "get|/parameter/queryAccount": account
};
