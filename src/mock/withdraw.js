// crd/user/queryCustFinancialStatus
// 查询冻结状态
const freeze = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    freeze: false, // 是否冻结
    freezeExpireTime: null, // "yyyy-MM-dd HH:mm:ss 冻结截止时间"
    freezeMsg: null // 冻结提示语
  }
};

const exclusive = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    applyNo: null,
    mutuallyExclusive: null
  }
};

export default {
  "get|/parameter/freeze": freeze,
  "get|/parameter/exclusive": exclusive
};
