const couponList = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    /**
       * useScene --- 券的类型
       * 1 提现页面---利息减免券
        2 还款页面---利息减免券
        4 还款页面---还款复借券
        3 提现还款 这种场景就会给提现跟还款的券查出来，目前用不上
        5 提额降息券
       * 
       * couponMethod --- 金额的类型
       * A: 减免固定金额
       * R: 减免百分比
       * 
       * amt --- 金额或百分比 
       * 
       * status --- 券的状态
       * used ：已使用
       * unUsed: 未使用
       * expired： 已过期
       * 
       * expiredDate --- 失效日期
       * 
       * reducedAmount --- 启用优惠券的金额，比如提现金额大于1000 ，此优惠券可用
       */
      oriCouponList: [
        { couponId: '001', title: 'Available for all loans', useScene: '1', couponMethod: 'A', amt: 500, expiredDate: '2023-09-26', status: 'unUsed', check: false, reducedAmount: 10000, enable: true },
        { couponId: '002', title: 'repay and get another loan', useScene: '1', couponMethod: 'A', amt: 200, expiredDate: '2023-09-05', status: 'unUsed', check: false, reducedAmount: 100000, enable: true },
        { couponId: '003', title: 'repay and get another loan', useScene: '1', couponMethod: 'R', amt: 0.3, expiredDate: '2023-09-05', status: 'unUsed', check: false, reducedAmount: 100000, enable: true },
        { couponId: '004', title: 'Available for all loans', useScene: '1', couponMethod: 'R', amt: 0.1, expiredDate: '2023-09-20', status: 'unUsed', check: false, reducedAmount: 100000, enable: true },
        { couponId: '005', title: 'repay and get another loan', useScene: '1', couponMethod: 'R', amt: 0.5, expiredDate: '2023-09-05', status: 'unUsed', check: false, reducedAmount: 100000, enable: true },
        { couponId: '006', title: 'Available for all loans', useScene: '1', couponMethod: 'A', amt: 60000, expiredDate: '2023-10-22', status: 'unUsed', check: false, reducedAmount: 100000, enable: true }
      ]
  }
};

export default {
  "get|/parameter/queryCouponList": couponList
};