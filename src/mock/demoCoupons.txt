/**
     * 优惠券排序逻辑
     * 1、系统默认勾选的或者用户自己勾选的优惠券置顶显示
     * 2、除了系统默认和用户自己选的置顶，其它优惠券按until到期时间先到期的在上，后到期的在下，依次排列：
     *    IF 到期时间一样，都是金额券，减免金额大的在上，金额小的在下
     *    IF 到期时间一样，都是利息百分比减免券，减免百分比大的在上，小的在下
     *    IF 到期时间一样，金额和利息百分比减免券都有，金额在上（金额类的同上），百分比在下（百分比内部同上）
     */
sortCouponList() {
  const oriCouponList = [
    { couponId: '001', title: 'Available for all loans', useScene: '1', couponMethod: 'A', amt: 500, expiredDate: '2023-09-26', status: 'unUsed', check: false, reducedAmount: 10000, enable: true },
    { couponId: '002', title: 'repay and get another loan', useScene: '1', couponMethod: 'A', amt: 200, expiredDate: '2023-09-05', status: 'unUsed', check: false, reducedAmount: 100000, enable: true },
    { couponId: '003', title: 'repay and get another loan', useScene: '1', couponMethod: 'R', amt: 0.3, expiredDate: '2023-09-05', status: 'unUsed', check: false, reducedAmount: 100000, enable: true },
    { couponId: '004', title: 'Available for all loans', useScene: '1', couponMethod: 'R', amt: 0.1, expiredDate: '2023-09-20', status: 'unUsed', check: false, reducedAmount: 100000, enable: true },
    { couponId: '005', title: 'repay and get another loan', useScene: '1', couponMethod: 'R', amt: 0.5, expiredDate: '2023-09-05', status: 'unUsed', check: false, reducedAmount: 100000, enable: true },
    { couponId: '006', title: 'Available for all loans', useScene: '1', couponMethod: 'A', amt: 60000, expiredDate: '2023-10-22', status: 'unUsed', check: false, reducedAmount: 100000, enable: true }
  ]
  if (oriCouponList.length > 0) {
    // 先按金额排序，从大到小
    oriCouponList.sort((a, b) => {
      return b.amt - a.amt
    })
    // 再按失效日期排序，先失效的在前面
    oriCouponList.sort((a, b) => {
      // eslint-disable-next-line no-useless-escape
      const day1 = (new Date(a.expiredDate.replace(/-/g, "\/")))
      // eslint-disable-next-line no-useless-escape
      const day2 = (new Date(b.expiredDate.replace(/-/g, "\/")))
      if (day1 === day2) {
        return b.amt - a.amt
      } else {
        return day1 < day2 ? -1 : 1
      }
    })
  }
  this.couponList = oriCouponList

  console.log('排序后的券列表', oriCouponList)
}