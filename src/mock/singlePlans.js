const singlePlans = {
  status: {
    code: 0,
    msg: "OK"
  },
  data: {
    amtPaid: 0,
    amtOutstd: 18599,
    actualAmtOutstd: 18599,
    remainingRepaymentAmount: 0,
    principal: 10000,
    interest: 7000,
    defaultInterest: 1363.8,
    vatAmount: 211.07,
    txnScene: null,
    coolingOffINTFreeFlag: null,
    serviceCharge: 1363.8,
    penaltyCharge: 24,
    promiseValidDate: null,
    loanList: [
      {
        loanId: "523062593180405760",
        term: 1,
        amtPaid: 0,
        amt: 18599,
        actualAmt: 18599,
        txnStatus: null,
        coolingOffINTFreeFlag: false,
        txnScene: null,
        loanDay: 0,
        requestSessionId: null,
        plans: [
          {
            overdueInterest: "0.00",
            fees: [
              {
                VAT_FEE_PAID: "0.00",
                VAT_FEE_OUTSTD: "211.07"
              }
            ],
            overdueInterestOutstd: "1200.00",
            dueDate: "2023-02-05",
            termRemainAmtOutstd: 18599,
            principalPaid: "0.00",
            amountActualOutstd: 18599,
            amountPaid: "0.00",
            amountOutstd: 18599,
            termStatus: "O",
            term: 1,
            principalOutstd: "10000.00",
            interestOutstd: "7000.00",
            interestPaid: "0.00"
          }
        ],
        feeOutstd: 211.07
      }
    ],
    repayAmtTipMsg:
      "The min repayment amount is GHC 1, pls reserve sufficient fee for Mpesa channel fee, pls repay on time, overdue will affert personal credit"
  }
};

// 坦桑，单期还款详情
const sing = [
  {
    overdueInterest: "0.00",
    fees: [
      { INSURANCE_FEE_PAID: "0.00", INSURANCE_FEE_OUTSTD: "600.00" },
      { VAT_FEE_PAID: "0.00", VAT_FEE_OUTSTD: "150.00" }
    ],
    insuranceFeeOutstd: 600,
    overdueInterestOutstd: "0.00",
    dueDate: "2026-05-06",
    termRemainAmtOutstd: 86625,
    principalPaid: "0.00",
    amountActualOutstd: 86625,
    amountPaid: "0.00",
    amountOutstd: 86625,
    termStatus: "N",
    term: 1,
    principalOutstd: "50000.00",
    interestOutstd: "35000.00",
    interestPaid: "0.00",
    penaltyCharge: undefined,
    serviceCharge: undefined,
    reduceBeforTotalOutstdBal: 86625,
    totalOutstd: 86625,
    interestRec: "35000.00",
    vatRec: undefined,
    status: "N",
    canCheck: true,
    checked: true
  }
];

export default {
  "get|/parameter/singlePlans": singlePlans
};
