/*
 * @Descripttion:
 * @version:
 * @Author:
 * @email: 
 * @Date: 
 * @LastEditors: 
 * @LastEditTime:
 */
const demoList = {
  status: 200,
  message: 'success',
  data: [{
      id: 1,
      name: '李世民',
      age: '23',
      job: '前端工程师'
  }, {
      id: 2,
      name: '朱元璋',
      age: '27',
      job: '后端工程师'
  }]
};
const demoList2 = [{
  id: 1,
  name: '铁木真',
  age: '26',
  job: '测试工程师'
}, {
  id: 2,
  name: '刘邦',
  age: '32',
  job: '产品经理'
}];
export default {
  'get|/parameter/query1': demoList,
  // 也可以这样写
  // 官方解释为：记录用于生成响应数据的函数。当拦截到匹配 rurl 和 rtype 的 Ajax 请求时，函数 function(options) 将被执行，并把执行结果作为响应数据返回。
  'get|/parameter/query2': (option) => {
      // 可以在这个地方对demoList2进行一系列操作，例如增删改
      // option 指向本次请求的 Ajax 选项集，含有 url、type 和 body 三个属性
      return {
          status: {
            code: 0,
            msg: 'OK'
          },
          data: demoList2
      };
  }
}