# 员工管理APP

## 技术方案
[技术方案](TECHNICAL_DESIGN.md)

## 技术栈
- Node 14
- Vue 2.6.11
- Vue-cli 5.0.0
- Vant 2.12.44（按需加载）
- Sass 1.32.13
- Axios 0.21.1
- Vue Router 3.4.7
- Vuex 3.4.0
- Postcss-pxtorem 5.1.1
- ESLint 7.32.0

## 特性
- 响应式布局适配
- 多语言国际化支持
- 原生接口桥接配置
- Webpack分包优化
- 组件化开发架构

## 使用
1. 下载依赖，不要提交package-lock.json文件
  npm install  
2. 本地开发  
  npm run dev  
3. 打包  
  测试环境：npm run build:uat  
  生产环境：npm run build:prod  

## 构建配置

### 开发服务器
```javascript
// vue.config.js
devServer: {
  port: 5766,
  proxy: {
    '/dxe-service': {
      target: 'https://dxe.dexintec.cn',
      secure: true,
      changeOrigin: true
    }
  }
}
```

## 项目结构

├── public/               # 静态资源
│   ├── favicon.ico
│   └── index.html        # 入口模板
├── src/                  # 源代码
│   ├── api/              # 接口模块
│   │   └── native.js    # 原生接口实现
│   ├── assets/           # 静态资源
│   ├── components/       # 全局组件
│   ├── router/          # 路由配置
│   ├── store/           # Vuex状态管理
│   └── views/           # 页面组件
└── package.json          # 依赖管理

## 原生接口配置

参考文档：[员工管理App-原生H5接口](https://idzersv4on.feishu.cn/sheets/KwiVsBUVwhJgw5tWpPWcPpiUnqf?sheet=84bc9f)

### 实现规范
```javascript
// 标准接口实现模板（src/api/native.js）
export const 方法名 = async (params) => {
  try {
    const result = await executeWithCallback('原生方法名', params);
    return result;
  } catch (error) {
    showToast('操作失败');
    throw error;
  }
}

// 回调处理机制
const executeWithCallback = (methodName, data) => {
  return new Promise((resolve, reject) => {
    const callback = `${methodName}Callback`;
    window.quickSave[methodName](JSON.stringify(data), callback);
    window[callback] = (res) => {
      resolve(JSON.parse(res || '{}'));
    };
  });
};
```

### 错误处理规范
1. 所有接口必须使用try/catch包裹
2. 错误日志需包含方法名称和参数信息
3. 用户提示使用统一Toast组件
4. 向上抛出原始错误对象

### 参数传递规范
1. 复杂参数必须JSON序列化
2. 基本类型参数直接传递
3. 回调函数名通过getCallbackName生成
4. Android/iOS差异化处理需显式声明
```