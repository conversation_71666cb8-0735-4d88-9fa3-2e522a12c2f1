const webpack = require('webpack')
const TerserPlugin = require("terser-webpack-plugin");
const path = require('path')
const fs = require("fs");
const CompressionPlugin = require('compression-webpack-plugin')
const vConsolePlugin = require('vconsole-webpack-plugin')
const ModifyCssPlugin = require('./modifycssplugin.js');

function stringToHash(string) {
  let hash = 0;
  for (let i = 0; i < string.length; i++) {
    const char = string.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(36).substring(0, 8); // 转为36进制并截取8位
}

// 需要单独抽取配置出来，否则开发环境设置默认值会报错
const cssConfig = {
  loaderOptions: {
    scss: {
      // 注意: 在sass-loader v8 中，这个选项是 prependData
      // prependData: `@import "./src/assets/scss/index.scss";`,
      additionalData: `@import "@/assets/scss/index.scss";` // sass-loader v10.0.0 以上的版本
    },
  },
}

module.exports = {
  publicPath: process.env.VUE_APP_PUBLICPATH,
  outputDir: path.resolve(__dirname, './dist'),
  assetsDir: './static',
  lintOnSave: true,
  css: cssConfig,
  transpileDependencies: true,
  configureWebpack: config => {
    config.devtool = process.env.VUE_APP_SOURCE_MAP || false;
    if (process.env.VUE_APP_CONSOLE === 'true') {
      config.plugins.push(
        new vConsolePlugin({
          enable: true
        })
      )
    }
    if (process.env.VUE_APP_GZIP === 'true') {
      config.plugins.push(
        new CompressionPlugin({
          algorithm: 'gzip',
          test: /\.(js|css|html|json|ico|svg)$/, // 匹配文件名
          threshold: 10240, // 对超过10k的数据压缩
          minRatio: 0.8 // 压缩比
        })
      )
    }
    // 只在生产环境混淆，在测试环境混淆会导致无法运行(非新包不混淆)，发生产再打开
    if (process.env.NODE_ENV === 'production') {
      config.plugins.push(
        new ModifyCssPlugin()
      )
    }

    // 开启分离jsq
    config.optimization = {
      splitChunks: {
        chunks: 'all', // 表明选择哪些 chunk 进行优化。通用设置，可选值：all/async/initial。设置为 all 意味着 chunk 可以在异步和非异步 chunk 之间共享。
        minSize: 20000, // 允许新拆出 chunk 的最小体积
        maxAsyncRequests: 10, // 每个异步加载模块最多能被拆分的数量
        maxInitialRequests: 10, // 每个入口和它的同步依赖最多能被拆分的数量
        enforceSizeThreshold: 50000, // 强制执行拆分的体积阈值并忽略其他限制
        cacheGroups: {
          core: { // 第三方库，基础类库(不改动，优先打包)
            name: 'chunk-core',
            test: /[\\/]node_modules[\\/](vue|vue-router|vuex|axios|vue-i18n)[\\/]/, // 请注意'[\\/]'的用法，是具有跨平台兼容性的路径分隔符
            priority: 30, // 优先级，执行顺序就是权重从高到低
            chunks: 'initial' // 只打包最初依赖的第三方
          },
          libs: { // 第三方库，基础类库(不改动，优先打包)
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/](core-js|core-js-pure|regenerator-runtime|vant|vee-validate)[\\/]/, // 请注意'[\\/]'的用法，是具有跨平台兼容性的路径分隔符
            priority: 25, // 优先级，执行顺序就是权重从高到低
            chunks: 'initial' // 只打包最初依赖的第三方
          },
          bussLibs: { // 业务类库(尽量引入业务相关的，尽可能保持不变。)
            name: 'buss-libs',
            test: /[\\/]node_modules[\\/](vue-clipboard2|clipboard)[\\/]/,
            priority: 20,
            chunks: 'all'
          },
          utilLibs: { // 业务类库(尽量引入业务相关的，尽可能保持不变。)
            name: 'util-libs',
            test: /[\\/]node_modules[\\/](lodash|crypto-js)[\\/]/,
            priority: 15,
            chunks: 'all'
          },
          newAddLibs: { // 剩下的库(新增的保持在这里，不变更以上两个库。)
            name: 'new-add-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 5,
            chunks: 'all'
          },
          commons: {
            name: 'chunk-commons',
            minChunks: 2, // 拆分前，这个模块至少被不同 chunk 引用的次数
            priority: 0,
            reuseExistingChunk: true
          },
        }
      }
    }
    // 只在生产删除log.
    if (process.env.VUE_APP_CONSOLE === 'false') {
      config.optimization.minimizer = [
        new TerserPlugin({
          parallel: 4,
          terserOptions: {
            compress: {
              drop_console: true,
            },
            output: {
              comments: false,
            }
          },
          extractComments: false
        })
      ]
    }
  },
  chainWebpack: config => {
    config.optimization
      .minimizer('terser')
      .tap(args => {
        args[0].terserOptions.compress.drop_console = process.env.VUE_APP_CONSOLE !== 'true'
        return args
      });

    // vue 脚手架默认开启了 preload 与 prefetch，影响首屏加载速度
    config.plugins.delete('preload') // 删除默认的preload
    config.plugins.delete('prefetch') // 删除默认的prefetch
    if (process.env.VUE_APP_ENV === 'production') {
      // 计算filename
      const filename = function(path, assets) {
        // console.log('filename', path, assets)
        const contentHash = assets.contentHash;
        const hash = path.module ? (path.module.buildInfo ? path.module.buildInfo.hash : '') : '';
        const url = 'static/img/'+ stringToHash(contentHash + hash) + '[ext]';
        return url;
      };
      config.module
        .rule("images")
        .test(/\.(png|jpe?g|gif|webp|avif)(\?.*)?$/)
        .set("type", "asset")
        .set("generator", {
          filename,
        });
    }
    // vue-cli创建的webpack模板默认会将 10KB 以下的图片和字体文件转为base64
    // 让图片不转为beas64
    // config.module
    //   .rule('images')
    //   .use('url-loader')
    //   .loader('url-loader')
    //   .tap(options => Object.assign(options, { limit: 1 }))
  },
  devServer: {
    port: '5766',
    client: {
      overlay: false,
    },
    allowedHosts: 'all',
    // webSocketServer: false,
    proxy: {
      '/dxe-service': {
        target: 'https://dxe.dexintec.cn',
        secure: true,
        changeOrigin: true,
      },
    }
  }
}
