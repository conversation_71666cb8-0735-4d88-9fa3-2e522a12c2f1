class ModifyCssPlugin {
  apply(compiler) {
    compiler.hooks.emit.tapAsync('ModifyCssPlugin', (compilation, callback) => {
      // 遍历所有编译过的资源文件
      for (const filename in compilation.assets) {
        if (filename.endsWith('.css')) {
          // 获取 CSS 文件内容
          let cssContent = compilation.assets[filename].source();

          // 修改 CSS 文件内容
          const insertCss = 'Text' + '{color: red}'
          cssContent = cssContent + insertCss;

          // 更新 CSS 文件内容
          compilation.assets[filename] = {
            source: () => cssContent,
            size: () => cssContent.length,
          };
        }
      }
      callback();
    });
  }
}

module.exports = ModifyCssPlugin;
