{"name": "employee-management", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "lint": "vue-cli-service lint", "inspect": "vue-cli-service inspect --mode development > webpack.config.js", "build": "vue-cli-service build --no-module", "build:uat": "vue-cli-service build --mode uat --no-module", "build:prod": "vue-cli-service build --mode production --no-module", "report": "vue-cli-service build --mode production --report"}, "dependencies": {"@babel/runtime": "^7.12.1", "@turf/distance": "^7.2.0", "@turf/helpers": "^7.2.0", "axios": "^0.21.1", "big.js": "^6.2.1", "compressorjs": "^1.2.1", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "eruda": "^3.4.1", "js-base64": "^3.7.2", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "qs": "^6.14.0", "smoothscroll-polyfill": "^0.4.4", "timezone": "^1.0.23", "utc": "^0.1.0", "v-calendar": "^2.4.1", "vant": "^2.12.44", "vconsole": "^3.14.2", "vee-validate": "^3.4.15", "vue": "^2.6.11", "vue-awesome-progress": "^1.9.7", "vue-i18n": "^8.9.0", "vue-router": "^3.4.7", "vuex": "^3.4.0", "vuex-router-sync": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.1", "compression-webpack-plugin": "^6.0.3", "eslint": "^7.32.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.2", "eslint-plugin-vue": "^6.2.2", "free-google-translator-api": "^1.0.0", "javascript-obfuscator": "^4.0.2", "json5": "^2.2.1", "mockjs": "^1.1.0", "node-sass": "^4.12.0", "postcss-pxtorem": "^5.1.1", "sass-loader": "^12.0.0", "vconsole-webpack-plugin": "^1.5.2", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.8.0", "webpack-obfuscator": "^2.6.0"}}